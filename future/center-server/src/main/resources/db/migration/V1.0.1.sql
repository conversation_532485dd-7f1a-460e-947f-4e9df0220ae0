CREATE TABLE center_system_depart
(
    id          BIGINT NOT NULL,
    name        VARCHAR(100) NOT NULL,
    parent_id   BIGINT NULL,
    sort        INTEGER NULL,
    tenant_id   BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT NULL,
    update_time TIMES<PERSON>MP DEFAULT NULL,
    creator_id  BIGINT DEFAULT NULL,
    updater_id  BIGINT DEFAULT NULL,
    PRIMARY KEY (id)
);

COMMENT ON TABLE center_system_depart IS '部门表';
COMMENT ON COLUMN center_system_depart.id IS '主键';
COMMENT ON COLUMN center_system_depart.name IS '部门名称';
COMMENT ON COLUMN center_system_depart.parent_id IS '上级部门ID';
COMMENT ON COLUMN center_system_depart.sort IS '显示顺序';
COMMENT ON COLUMN center_system_depart.tenant_id IS '租户ID';
COMMENT ON COLUMN center_system_depart.create_time IS '创建时间';
COMMENT ON COLUMN center_system_depart.update_time IS '更新时间';
COMMENT ON COLUMN center_system_depart.creator_id IS '创建者';
COMMENT ON COLUMN center_system_depart.updater_id IS '更新者';

CREATE TABLE center_system_tenant
(
    id            BIGINT NOT NULL,
    name          VARCHAR(100) NOT NULL,
    description   VARCHAR(200) NOT NULL,
    status        VARCHAR(10) NOT NULL,
    expire_time   TIMESTAMP NOT NULL,
    account_count INTEGER NOT NULL,
    create_time   TIMESTAMP DEFAULT NULL,
    update_time   TIMESTAMP DEFAULT NULL,
    creator_id    BIGINT DEFAULT NULL,
    updater_id    BIGINT DEFAULT NULL,
    manager_id    BIGINT NOT NULL,
    PRIMARY KEY (id)
);

COMMENT ON TABLE center_system_tenant IS '租户表';
COMMENT ON COLUMN center_system_tenant.id IS '租户ID';
COMMENT ON COLUMN center_system_tenant.name IS '租户名';
COMMENT ON COLUMN center_system_tenant.description IS '备注说明';
COMMENT ON COLUMN center_system_tenant.status IS '租户状态';
COMMENT ON COLUMN center_system_tenant.expire_time IS '过期时间';
COMMENT ON COLUMN center_system_tenant.account_count IS '账号数量';
COMMENT ON COLUMN center_system_tenant.create_time IS '创建时间';
COMMENT ON COLUMN center_system_tenant.update_time IS '更新时间';
COMMENT ON COLUMN center_system_tenant.creator_id IS '创建者';
COMMENT ON COLUMN center_system_tenant.updater_id IS '更新者';
COMMENT ON COLUMN center_system_tenant.manager_id IS '租户管理员ID';


CREATE TABLE center_system_user
(
    id                  BIGINT NOT NULL,
    username            VARCHAR(50) NOT NULL,
    password            VARCHAR(100) NOT NULL DEFAULT '',
    display_name        VARCHAR(50) NOT NULL,
    status              VARCHAR(10) NOT NULL,
    login_ip            VARCHAR(100) DEFAULT '',
    login_time          TIMESTAMP DEFAULT NULL,
    create_time         TIMESTAMP DEFAULT NULL,
    update_time         TIMESTAMP DEFAULT NULL,
    creator_id          BIGINT DEFAULT NULL,
    updater_id          BIGINT DEFAULT NULL,
    tenant_id           BIGINT NOT NULL DEFAULT 0,
    depart_id           BIGINT NULL DEFAULT 0,
    is_deleted          INTEGER NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

COMMENT ON TABLE center_system_user IS '用户信息表';
COMMENT ON COLUMN center_system_user.id IS '用户ID';
COMMENT ON COLUMN center_system_user.username IS '用户账号';
COMMENT ON COLUMN center_system_user.password IS '密码';
COMMENT ON COLUMN center_system_user.display_name IS '用户名';
COMMENT ON COLUMN center_system_user.status IS '帐号状态';
COMMENT ON COLUMN center_system_user.login_ip IS '最后登录IP';
COMMENT ON COLUMN center_system_user.login_time IS '最后登录时间';
COMMENT ON COLUMN center_system_user.create_time IS '创建时间';
COMMENT ON COLUMN center_system_user.update_time IS '更新时间';
COMMENT ON COLUMN center_system_user.creator_id IS '创建者';
COMMENT ON COLUMN center_system_user.updater_id IS '更新者';
COMMENT ON COLUMN center_system_user.tenant_id IS '租户编号';
COMMENT ON COLUMN center_system_user.depart_id IS '部门id';
COMMENT ON COLUMN center_system_user.is_deleted IS '删除标识';

CREATE TABLE center_system_role
(
    id          BIGINT NOT NULL,
    name        VARCHAR(50) NOT NULL,
    code        VARCHAR(50) NOT NULL,
    sort        INTEGER NOT NULL,
    status      VARCHAR(10) DEFAULT NULL,
    remark      VARCHAR(500) DEFAULT NULL,
    tenant_id   BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT NULL,
    update_time TIMESTAMP DEFAULT NULL,
    creator_id  BIGINT DEFAULT NULL,
    updater_id  BIGINT DEFAULT NULL,
    PRIMARY KEY (id)
);

COMMENT ON TABLE center_system_role IS '角色信息表';
COMMENT ON COLUMN center_system_role.id IS '角色ID';
COMMENT ON COLUMN center_system_role.name IS '角色名称';
COMMENT ON COLUMN center_system_role.code IS '角色代码';
COMMENT ON COLUMN center_system_role.sort IS '显示顺序';
COMMENT ON COLUMN center_system_role.status IS '角色状态';
COMMENT ON COLUMN center_system_role.remark IS '备注';
COMMENT ON COLUMN center_system_role.tenant_id IS '租户编号';
COMMENT ON COLUMN center_system_role.create_time IS '创建时间';
COMMENT ON COLUMN center_system_role.update_time IS '更新时间';
COMMENT ON COLUMN center_system_role.creator_id IS '创建者';
COMMENT ON COLUMN center_system_role.updater_id IS '更新者';


CREATE TABLE center_system_menu
(
    id          BIGINT NOT NULL,
    name        VARCHAR(50) NOT NULL,
    category    VARCHAR(10) NOT NULL,
    sort        INTEGER NOT NULL DEFAULT 0,
    parent_id   BIGINT NOT NULL,
    path        VARCHAR(200) DEFAULT '',
    icon        VARCHAR(100) DEFAULT '#',
    status      VARCHAR(10) DEFAULT NULL,
    create_time TIMESTAMP DEFAULT NULL,
    update_time TIMESTAMP DEFAULT NULL,
    creator_id  BIGINT DEFAULT NULL,
    updater_id  BIGINT DEFAULT NULL,
    PRIMARY KEY (id)
);

COMMENT ON TABLE center_system_menu IS '菜单表';
COMMENT ON COLUMN center_system_menu.id IS '菜单ID';
COMMENT ON COLUMN center_system_menu.name IS '菜单名称';
COMMENT ON COLUMN center_system_menu.category IS '菜单类型';
COMMENT ON COLUMN center_system_menu.sort IS '显示顺序';
COMMENT ON COLUMN center_system_menu.parent_id IS '父菜单ID';
COMMENT ON COLUMN center_system_menu.path IS '路由地址';
COMMENT ON COLUMN center_system_menu.icon IS '菜单图标';
COMMENT ON COLUMN center_system_menu.status IS '菜单状态';
COMMENT ON COLUMN center_system_menu.create_time IS '创建时间';
COMMENT ON COLUMN center_system_menu.update_time IS '更新时间';
COMMENT ON COLUMN center_system_menu.creator_id IS '创建者';
COMMENT ON COLUMN center_system_menu.updater_id IS '更新者';

CREATE TABLE center_system_user_role
(
    id          BIGINT NOT NULL,
    user_id     BIGINT NOT NULL,
    role_id     BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT NULL,
    update_time TIMESTAMP DEFAULT NULL,
    creator_id  BIGINT DEFAULT NULL,
    updater_id  BIGINT DEFAULT NULL,
    PRIMARY KEY (id)
);

COMMENT ON TABLE center_system_user_role IS '用户和角色关联表';
COMMENT ON COLUMN center_system_user_role.id IS '主键ID';
COMMENT ON COLUMN center_system_user_role.user_id IS '用户ID';
COMMENT ON COLUMN center_system_user_role.role_id IS '角色ID';
COMMENT ON COLUMN center_system_user_role.create_time IS '创建时间';
COMMENT ON COLUMN center_system_user_role.update_time IS '更新时间';
COMMENT ON COLUMN center_system_user_role.creator_id IS '创建者';
COMMENT ON COLUMN center_system_user_role.updater_id IS '更新者';



CREATE TABLE center_system_role_menu
(
    id          BIGINT NOT NULL,
    role_id     BIGINT NOT NULL,
    menu_id     BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT NULL,
    update_time TIMESTAMP DEFAULT NULL,
    creator_id  BIGINT DEFAULT NULL,
    updater_id  BIGINT DEFAULT NULL,
    PRIMARY KEY (id)
);

COMMENT ON TABLE center_system_role_menu IS '角色和菜单关联表';
COMMENT ON COLUMN center_system_role_menu.id IS '主键ID';
COMMENT ON COLUMN center_system_role_menu.role_id IS '角色ID';
COMMENT ON COLUMN center_system_role_menu.menu_id IS '菜单ID';
COMMENT ON COLUMN center_system_role_menu.create_time IS '创建时间';
COMMENT ON COLUMN center_system_role_menu.update_time IS '更新时间';
COMMENT ON COLUMN center_system_role_menu.creator_id IS '创建者';
COMMENT ON COLUMN center_system_role_menu.updater_id IS '更新者';

INSERT INTO center_system_tenant
(id, name, description, status, expire_time, account_count, create_time, update_time, creator_id, updater_id,
 manager_id)
VALUES (1825838985241448448, '深圳大数据研院无锡创新中心', '深圳大数据研院无锡创新中心', 'ACTIVE',
        '2099-12-31 23:59:59', 100, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1825843512497688576, 1825843512497688576, 0);

INSERT INTO center_system_user
(id, username, password, display_name, status, login_ip, login_time,depart_id, create_time, update_time, creator_id, updater_id,
 tenant_id)
VALUES (1825843512497688576, 'superadmin', '867b67919f24871f189784d933d558df', '超级管理员', 'ACTIVE', NULL, NULL,
        0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1825843512497688576, 1825843512497688576, 1825838985241448448);

INSERT INTO center_system_role
(id, name, code, sort, status, remark, tenant_id, create_time, update_time, creator_id, updater_id)
VALUES (1826081597714087936, '超级管理员', 'SUPERADMIN', 1, 'ACTIVE', '系统超级管理员', 1825838985241448448,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1825843512497688576, 1825843512497688576);

INSERT INTO center_system_user_role
(id, user_id, role_id, create_time, update_time, creator_id, updater_id)
VALUES (1826082885449302016, 1825843512497688576, 1826081597714087936, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
        1825843512497688576, 1825843512497688576);