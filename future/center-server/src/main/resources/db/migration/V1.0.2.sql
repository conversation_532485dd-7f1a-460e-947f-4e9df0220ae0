CREATE TABLE center_system_dict_type
(
    `id`          bigint   NOT NULL COMMENT ' 附件唯一标识 ',
    `dict_type`   VARCHAR(255) NULL COMMENT ' 字典类型标识如 INDUSTRY ',
    `dict_name`   VARCHAR(255) NULL COMMENT ' 字典类型名称如“行业分类”',
    `status`      VARCHAR(20) NULL COMMENT '状态（ACTIVE, INACTIVE）',
    `remark`      VARCHAR(255) NULL COMMENT '备注信息',
    `tenant_id`   bigint   NOT NULL COMMENT '租户ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id`  bigint DEFAULT NULL COMMENT '创建者',
    `updater_id`  bigint DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT ='字典类型表';

CREATE TABLE center_system_dict_data
(
    `id`          bigint   NOT NULL COMMENT '附件唯一标识',
    `dict_id`     bigint   NULL COMMENT '字典类型表',
    `dict_label`  VARCHAR(255) NULL COMMENT '显示的标签名称，如“物业行业”',
    `dict_value`  VARCHAR(255) NULL COMMENT '实际存储值，如"PROPERTY"',
    `is_default`  INT NULL COMMENT '是否默认项（0=否, 1=是）',
    `parent_id`   bigint   NULL COMMENT '父级ID，用于层级结构（0=顶级）',
    `status`      VARCHAR(20) NULL COMMENT '状态（ACTIVE, INACTIVE）',
    `sort`        INT NULL COMMENT '排序字段，数值越小越靠前',
    `remark`      VARCHAR(255) NULL COMMENT '备注信息',
    `tenant_id`   bigint   NOT NULL COMMENT '租户ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id`  bigint DEFAULT NULL COMMENT '创建者',
    `updater_id`  bigint DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT ='字典数据表';


INSERT INTO `center_system_dict_type`
(id, dict_type, dict_name, status, remark, tenant_id, create_time, update_time, creator_id, updater_id)
VALUES (1873900868047208448, 'INDUSTRY', '行业分类', 1, '行业分类', 1825838985241448448, current_timestamp,
        current_timestamp, 1825843512497688576, 1825843512497688576);

INSERT INTO `center_system_dict_data`
(id, dict_id, dict_label, dict_value, is_default, parent_id, status, sort, remark, tenant_id, create_time, update_time,
 creator_id, updater_id)
VALUES (1873900868051402753, 1873900868047208448, '通用行业', 'GENERAL', 1, 0, 1, 0, '通用行业', 1825838985241448448,
        current_timestamp, current_timestamp, 1825843512497688576, 1825843512497688576);

INSERT INTO `center_system_dict_data`
(id, dict_id, dict_label, dict_value, is_default, parent_id, status, sort, remark, tenant_id, create_time, update_time,
 creator_id, updater_id)
VALUES (1873900868051402754, 1873900868051402752, '通用数据', 'GENERAL', 1, 0, 1, 0, '通用数据', 1825838985241448448,
        current_timestamp, current_timestamp, 1825843512497688576, 1825843512497688576);


update center_system_dict_type
set status = 'ACTIVE';

update center_system_dict_data
set status = 'ACTIVE';

update center_system_dict_type
set dict_type='CLASSIFICATION',
    dict_name='分类',
    status='ACTIVE'
where id = 1873900868047208448;

update center_system_dict_data
set dict_label = '其他',
    dict_value = 'OTHER',
    remark='其他分类'
where id = 1873900868051402753;