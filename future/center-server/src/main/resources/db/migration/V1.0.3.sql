CREATE TABLE `center_short_message`
(
    `id`           bigint                                 NOT NULL COMMENT '主键',
    `phone_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号',
    `sms_code`     varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机验证码',
    `content`      varchar(100) COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
    `status`       varchar(10) COLLATE utf8mb4_unicode_ci NULL COMMENT '状态:已使用/未使用',
    `create_time`  datetime DEFAULT NULL COMMENT '创建时间',
    `update_time`  datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id`   bigint   DEFAULT NULL COMMENT '创建者',
    `updater_id`   bigint   DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='手机验证码';