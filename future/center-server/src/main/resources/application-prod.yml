spring:
  datasource:
    url: *****************************************************************************************************************************************************
    username: root
    password: center#bf9b#0006   #密码未改动

  flyway:
    url: *******************************************************************************************************************************************
    user: root
    password: center#bf9b#0006
    table: future_flyway_schema_history
  #    flyway元数据表名称，如果不指定，则默认为flyway_schema_history，多个系统共用一个库时，可以通过使用不再的表来控制数据库脚本的版本。
  #    同一个表的元数据，最好是由一个系统进行维护
  # Redis 配置
  data:
    redis:
      host: 127.0.0.1 # 地址
      port: 6379 # 端口
      database: 1 # 数据库索引
#    password: dev # 密码，建议生产环境开启
  autoconfigure:
    exclude:
      - #排除redis自动配置类，避免redis自动创建，当启用redis时注释掉下方代码
      - org.redisson.spring.starter.RedissonAutoConfigurationV2
cache:
  type: memory
#  -- #################### 接口文档配置（生产环境关闭-false） ####################
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

# 或者 minio, ftp, oss 等
storage:
  type:
#hdfs配置
hadoop:
  base-path: /metahuman
  fs:
    defaultFS: hdfs://**********:8020
  dfs:
    client:
      use-datanode-hostname: true
  user:
    name: root
  home:
    dir: /usr/local/backend/center/hadoop_home
  base:
    url: http://**********:9870/webhdfs/v1/metahuman
    query: ?op=OPEN

#minio配置
minio:
  # MinIO的服务地址
  endpoint: http://**********:8002
  downloadEndpoint: http://*************:8002
  #生产环境的minIO不支持https，只能通过nginx进行转发。
  downloadEndpointNginx: https://www.moshuspace.cn/oss-platform
  # MinIO的访问密钥
  access-key: admin
  # MinIO的秘密密钥
  secret-key: minio%0510
  bucket: metahuman-prod
###################业务逻辑相关配置##########################
#创建企业管理员用户初始密码
admin:
  password: e10adc3949ba59abbe56e057f20f883e
password:
  default: 123456
center:
  base_url: http://*************
  chat: ${center.base_url}:6000/chat
preview:
  url: /metahuman/file/preview
metahuman:
  api:
    url: http://**********:4001/api/triageV3/question/stream
  system:
    metahumanId: 1912699161486176256
    sceneId: 1912691906544803840
    avatarId: 1912700936133947392
  voice:
    text: 你好，我是数字人小江。
nginx:
  previewUrl: https://www.moshuspace.cn/center-images/images/metahuman/
  logPath: /usr/local/nginx/html/logs/metahuman/
  localBasePath: /usr/local/nginx/html/images/metahuman/