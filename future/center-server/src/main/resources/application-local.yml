spring:
  datasource:
    url: ****************************************************************************************************************************************************************************
    username: nsodev
    password: Nsodev#2025@

  flyway:
    url: ******************************************************************************************************************************************************************
    user: nsodev
    password: Nsodev#2025@
    table: future_flyway_schema_history
  #    flyway元数据表名称，如果不指定，则默认为flyway_schema_history，多个系统共用一个库时，可以通过使用不再的表来控制数据库脚本的版本。
  #    同一个表的元数据，最好是由一个系统进行维护
  # Redis 配置
  data:
    redis:
      host: 127.0.0.1 # 地址
      port: 6379 # 端口
      database: 1 # 数据库索引
      password: 123456 # 密码，建议生产环境开启
    #设置本地缓存或中央缓存
  autoconfigure:
    exclude:
      - #排除redis自动配置类，避免redis自动创建，当启用redis时注释掉下方代码
      - org.redisson.spring.starter.RedissonAutoConfigurationV2
  ai:
    openai:
      api-key: sk-ddb36933d9f84868a2164a01e16a55be
      base-url: https://api.deepseek.com
      chat:
        options:
          model: deepseek-chat
          temperature: 0.7
#设置本地缓存或中央缓存
cache:
  type: memory
#  -- #################### 接口文档配置（生产环境关闭-false） ####################

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

#hdfs配置
# 或者 minio, ftp, oss 等
storage:
  type:
#hadoop:
#  base-path: /algorithm
#  fs:
#    defaultFS: hdfs://*************:8020
#  dfs:
#    client:
#      use-datanode-hostname: true
#  user:
#    name: root
#  home:
#    dir: D:\sss
#  base:
#    url: http://*************:9870/webhdfs/v1/algorithm
#    query: ?op=OPEN

#minio配置
minio:
  # MinIO的服务地址
  endpoint: http://**********:19100
  downloadEndpoint: http://*************:8002
  #生产环境的minIO不支持https，只能通过nginx进行转发。
  downloadEndpointNginx: http://*************:9083/oss-platform
  # MinIO的访问密钥
  access-key: admin
  # MinIO的秘密密钥
  secret-key: minio%0510
  bucket: metahuman


###################业务逻辑相关配置##########################
#创建企业管理员用户初始密码
admin:
  password: e10adc3949ba59abbe56e057f20f883e
password:
  default: 123456


preview:
  url: /metahuman/file/preview

center:
  base_url: http://*************
  chat: ${center.base_url}:6000/chat
metahuman:
  api:
    url: http://*************:4001/api/triageV3/question/stream
  system:
    metahumanId: 1912699161486176256
    sceneId: 1912691906544803840
    avatarId: 1912700936133947392
  voice:
    text: 你好，我是数字人小江。

nginx:
  previewUrl: http://112.25.82.234:8000/images/metahuman/
  logPath: /usr/local/nginx/html/logs/metahuman/
  localBasePath: /usr/local/nginx/html/images/metahuman/