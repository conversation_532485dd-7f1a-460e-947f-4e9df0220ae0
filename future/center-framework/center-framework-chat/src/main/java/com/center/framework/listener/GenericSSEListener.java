package com.center.framework.listener;

import com.center.framework.paser.ChatSseParser;
import com.center.framework.paser.ParsedEvent;
import com.center.framework.paser.StreamParser;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import java.io.IOException;
import java.util.concurrent.CountDownLatch;


/**
 * 通用 SSE 监听器
 * - 订阅SSE流数据，并交给 StreamParser 解析
 * - 按照解析结果 (ParsedEvent) 将内容发送给前端 (SseEmitter)
 * - 可累积整体回答 (accumulatedAnswer) 供业务层使用
 */
@Slf4j
public class GenericSSEListener extends EventSourceListener {

    protected final SseEmitter emitter;
    protected final StreamParser parser;

    @Getter
    protected final CountDownLatch latch;

    protected final StringBuilder accumulatedAnswer = new StringBuilder();
    protected boolean completed;
    // 用于存储最终生成的回答内容
    private final StringBuilder builder = new StringBuilder();

    public GenericSSEListener(SseEmitter emitter, StreamParser parser, CountDownLatch latch) {
        this.emitter = emitter;
        this.parser = parser;
        this.latch = latch;
    }


    @Override
    public void onOpen(EventSource eventSource, Response response) {
        log.info("SSE连接已建立: {}", response.request().url());
    }

    @Override
    public void onEvent(EventSource eventSource, String id, String eventType, String data) {
        if (completed) {
            log.warn("已完成又收到数据: {}, 忽略", data);
            return;
        }
        try {
            // 判断响应数据的格式
            ChatSseParser chatSseParser = new ChatSseParser(emitter, completed, builder);
            ParsedEvent parsedEvent = chatSseParser.parse(data, eventType);
            handleParsedEvent(parsedEvent);

        } catch (Exception e) {
            log.error("解析数据出错: {}, eventType={}", data, eventType, e);
            handleError("解析异常: " + e.getMessage());
            eventSource.cancel();
        }
    }

    @Override
    public void onClosed(EventSource eventSource) {
        log.info("SSE连接关闭: {}", eventSource.request().url());
        if (!completed) {
            complete();
        }
        latch.countDown();
    }

    @Override
    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        log.error("SSE流式通信失败: {}", (t != null ? t.getMessage() : "null"), t);
        if (response != null) {
            log.error("响应码: {}, body: {}", response.code(), response.message());
        }
        handleError("通信失败");
        latch.countDown();
    }

    protected void handleParsedEvent(ParsedEvent event) {
        switch (event.getEventType()) {
            case MESSAGE:
                if (event.getContent() != null) {
                    accumulatedAnswer.append(event.getContent());
                    sendSse("message", event.getContent());
                }
                break;

            case SPAM:
                sendSse("spam", event.getContent());
                complete();
                break;

            case END:
                // 可能还有最后一段content
                if (event.getContent() != null) {
                    accumulatedAnswer.append(event.getContent());
                    sendSse("message", event.getContent());
                }
                sendSse("report", "对话结束");
                complete();
                break;

            case ERROR:
                handleError(event.getContent());
                break;

            case UNKNOWN:
            default:
                log.warn("未知事件: {}", event.getRawData());
                // 这里可以不发送给前端，也可以 sendSse("unknown", ...)
                break;
        }
    }

    protected void sendSse(String name, String data) {
        try {
            emitter.send(SseEmitter.event().name(name).data(data));
        } catch (IOException e) {
            log.error("发送SSE失败", e);
            complete();
        }
    }

    protected void handleError(String errMsg) {
        if (!completed) {
            sendSse("error", errMsg);
            complete();
        }
    }

    protected void complete() {
        if (!completed) {
            try {
                emitter.complete();
            } catch (Exception e) {
                log.error("SSE complete异常", e);
            }
            completed = true;
        }
    }

    public String getAccumulatedAnswer() {
        return accumulatedAnswer.toString();
    }
}