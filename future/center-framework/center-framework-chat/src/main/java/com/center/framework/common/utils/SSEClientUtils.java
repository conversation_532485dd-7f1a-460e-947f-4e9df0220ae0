package com.center.framework.common.utils;

import com.center.framework.listener.GenericSSEListener;
import com.center.framework.web.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSources;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION;

@Slf4j
public class SSEClientUtils {

    private static final ExecutorService SSE_EXECUTOR = Executors.newCachedThreadPool();

    private SSEClientUtils() {
        // 私有化构造器，防止外部实例化
    }

    /**
     * 通用异步执行SSE请求
     *
     * @param emitter  SseEmitter
     * @param url      请求地址
     * @param param    请求参数
     * @param listener SSE监听器
     */
    public static void runSSEAsync(SseEmitter emitter, String url, HashMap<String, Object> param, GenericSSEListener listener) {
        CompletableFuture.runAsync(() -> {
            try {
                OkHttpClient client = OkHttpUtils.getOkHttpClient();
                Request request = OkHttpUtils.createRequest(url, param, "app-inDpPgnKEHyqZH7eTJHo5XAx");

                EventSource.Factory factory = EventSources.createFactory(client);
                factory.newEventSource(request, listener);

                // 阻塞等待 SSE 完成
                listener.getLatch().await();
            } catch (Exception e) {
                log.error("请求SSE异常", e);
                try {
                    emitter.send(SseEmitter.event().name("error")
                            .data(CommonResult.error(OUTER_SERVER_EXCEPTION.getCode(), "对话异常，请稍后再试", e.getMessage())));
                } catch (IOException ex) {
                    log.error("发送SSE错误事件失败", ex);
                }
            }
        }, SSE_EXECUTOR);
    }
}

