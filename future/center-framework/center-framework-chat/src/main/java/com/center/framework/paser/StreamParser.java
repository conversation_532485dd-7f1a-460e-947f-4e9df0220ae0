package com.center.framework.paser;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 流式解析器接口
 */
public interface StreamParser {
    
    /**
     * 解析事件数据
     * @param rawData 原始数据
     * @param eventType 事件类型
     * @return 解析后的事件
     * @throws Exception 解析异常
     */
    ParsedEvent parse(String rawData, String eventType) throws Exception;
    
    /**
     * 处理垃圾信息响应
     * @param message 消息内容
     */
    void handleSpamResponse(String message);
    
    /**
     * 获取SSE发射器
     * @return SSE发射器
     */
    default SseEmitter getEmitter() {
        return null;
    }
    
    /**
     * 获取累积的回答
     * @return 累积的回答
     */
    default StringBuilder getAnswerBuilder() {
        return null;
    }
    
    /**
     * 是否启用垃圾检测
     * @return 是否启用垃圾检测
     */
    default boolean isSpamCheckEnabled() {
        return false;
    }
}
