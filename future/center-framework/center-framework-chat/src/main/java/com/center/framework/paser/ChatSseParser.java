package com.center.framework.paser;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.enumeration.ParsedEventType;
import com.center.framework.web.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION;


@Slf4j
public class ChatSseParser implements StreamParser {
    private static final String CHAT_DEFAULT_ERROR_MESSAGE = "系统正忙，请稍后再试。";
    private static final String CHAT_DEFAULT_SPAMERROR_MESSAGE = "我们的平台致力于提供一个安全、尊重的环境，某些话题可能不适宜讨论。我会很乐意帮助您解答其他问题，谢谢您的理解和配合。";


    private final SseEmitter emitter;
    private boolean completed;
    private StringBuilder builder;

    public ChatSseParser(SseEmitter emitter, boolean completed, StringBuilder builder) {
        this.completed = completed;
        this.emitter = emitter;
        this.builder = builder;
    }


    @Override
    public ParsedEvent parse(String rawData, String eventType) throws Exception {
        ParsedEvent event = new ParsedEvent();
        event.setRawData(rawData);
        event.setOriginalEventType(eventType); // 将原始事件名保存
        JSONObject jsonObject = JSONUtil.parseObj(rawData);

        //先判断是否出现算法报错
        if (jsonObject.containsKey("error") || jsonObject.containsKey("status_code")) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED);
        }

        if (jsonObject.getStr("event") != null && jsonObject.getStr("event").equals("message")) {
            //推荐时走这里
            return parseMessage(rawData, event);
        } else {
            //对话+带有文件的对话应该走这里
            return parseOther(rawData, event);
        }
    }


    /**
     * 模拟原先 handleMessageEvent
     */
    private ParsedEvent parseMessage(String rawData, ParsedEvent event){
        event.setEventType(ParsedEventType.MESSAGE);

        // 解析 JSON => 取 content
        JSONObject jsonObject = JSONUtil.parseObj(rawData);
        String content = jsonObject.getStr("answer");
        String conversationId = jsonObject.getStr("conversation_id");

        if (StringUtils.isNotEmpty(content)) {
            builder.append(content);
            event.setContent(content);
        } else {
            log.warn("message事件未找到content字段, rawData={}", rawData);
        }
        event.setConversationId(conversationId);
        return event;
    }


    /**
     * 模拟 handleOtherEvent
     */
    private ParsedEvent parseOther(String rawData, ParsedEvent event){
        // 先默认 MESSAGE, 也可视情况设 UNKNOWN
        event.setEventType(ParsedEventType.MESSAGE);

        if (!isCompleteJson(rawData)) {
            log.warn("其他类型事件数据非完整JSON: {}", rawData);
            // 把它设为UNKNOWN
            event.setEventType(ParsedEventType.UNKNOWN);
            return event;
        }
        // 如果是json, 可能还有content
        JSONObject jsonObject = JSONUtil.parseObj(rawData);
        String text = jsonObject.getStr("text");
        String conversationId = jsonObject.getStr("conversation_id");
        if (text != null) {
            builder.append(text);
            event.setContent(text);
        } else {
            log.warn("other事件中未找到text字段, rawData={}", rawData);
        }
        event.setConversationId(conversationId);
        return event;
    }


    /**
     * 处理敏感词情况，立即结束SSE
     */
    @Override
    public void handleSpamResponse(String message) {
        if (completed) {
            log.warn("已经完成的SSE尝试再次spam结束，忽略");
            return;
        }

        try {
            //chatService.saveQuestionAndAnswerAndSession(chatVO, builder.toString());
            emitter.send(SseEmitter.event().name("spam").data(message));
            emitter.send(SseEmitter.event().name("end").data(CommonResult.success()));
        } catch (IOException e) {
            log.error("spam事件写出失败", e);
            handleException("spam事件写出失败：" + e.getMessage());
        }
    }


    /**
     * 处理异常并向前端发送error事件
     */
    private void handleException(String errorTrace) {
        if (completed) {
            log.warn("已完成的SSE再次试图发送error，忽略");
            return;
        }

        try {
            emitter.send(
                    SseEmitter.event()
                            .name("error")
                            .data(
                                    CommonResult.error(
                                            OUTER_SERVER_EXCEPTION.getCode(),
                                            CHAT_DEFAULT_ERROR_MESSAGE,
                                            errorTrace)));
        } catch (IOException e) {
            log.error("处理异常时发送失败: {}", e.getMessage(), e);
        } finally {
            safeComplete();
        }
    }


    /**
     * 安全完成SSE，不重复完成
     */
    private void safeComplete() {
        if (!completed) {
            emitter.complete();
            completed = true;
        }
    }

    private boolean isCompleteJson(String json) {
        try {
            JSONUtil.parseObj(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
