package com.center.check;

import com.center.framework.web.pojo.CommonResult;
import com.center.check.service.CheckService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Map;

@Tag(name = "短信图形验证")
@RestController
@RequestMapping("/check")
@Validated
public class CheckController {

    @Resource
    private CheckService checkService;

    @Operation(summary = "获取图形验证码base64")
    @GetMapping("/getVerifyImage")
    public CommonResult<Map<String, String>> getVerifyImage() {
        return CommonResult.success(checkService.identifyImage());
    }

    @Operation(summary = "校验图形验证码")
    @PostMapping("/validateCaptcha")
    public CommonResult<String> validateCaptcha(@RequestParam String codeId, @RequestParam String verifyCode) {
        checkService.validateCaptcha(codeId,verifyCode);
        return CommonResult.success("验证码校验成功");
    }

}
