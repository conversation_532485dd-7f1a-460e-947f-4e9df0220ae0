package com.center.check.service;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import com.center.cache.memory.MemoryHashCache;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class CheckServiceImpl implements CheckService {

    @Resource
    private MemoryHashCache memoryHashCache;

    @Override
    public Map<String, String> identifyImage() {
        //1. 定义图形验证码的长和宽
        LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(200, 90, 4, 100);
        String code = lineCaptcha.getCode();
        log.info("验证码 {}",code);
        //2. 生成唯一标识 codeId
        String codeId = UUID.randomUUID().toString();
        //3. 将 codeId 和验证码值存储到缓存
        memoryHashCache.put(codeId, code,5, TimeUnit.MINUTES); //设置 5 分钟过期时间

        //4. 返回 Base64 图片和 codeId
        Map<String, String> result = new HashMap<>();
        result.put("codeId", codeId);
        result.put("image", lineCaptcha.getImageBase64Data());
        return result;
    }

    @Override
    public void validateCaptcha(String codeId, String verifyCode) {
        //1. 从缓存中获取正确的验证码值
        String correctCode = (String) memoryHashCache.get(codeId);

        if (correctCode == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "验证码为空或失效");
        }
        if (!correctCode.equalsIgnoreCase(verifyCode)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "验证码错误");
        }
        //2. 校验通过，清除缓存中的验证码
        memoryHashCache.remove(codeId);
    }
}
