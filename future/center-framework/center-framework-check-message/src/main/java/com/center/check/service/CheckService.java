package com.center.check.service;

import java.util.Map;

public interface CheckService {

     /**
      * 生成图形验证码并返回 Base64 图片及验证码标识
      *
      * @return 包含验证码信息的 Map
      * @throws RuntimeException 如果生成验证码或缓存操作失败
      */
     Map<String, String> identifyImage();

     /**
      * 校验用户输入的图形验证码
      *
      * @param codeId   验证码唯一标识（由 {@link #identifyImage()} 返回）
      * @param verifyCode 用户输入的验证码
      */
     void validateCaptcha(String codeId, String verifyCode);
}
