package com.center.framework.web.interceptor;

import com.center.framework.common.context.LoginContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class ServerInterceptor implements HandlerInterceptor {

  @Value("${metahuman.superadmin-id}")
  private Long superAdminId;

  @Value("${metahuman.tenant-id}")
  private Long tenantId;

  @Override
  public boolean preHandle(HttpServletRequest request,
      HttpServletResponse response, Object arg2) throws Exception {
    LoginContextHolder.setLoginUserId(superAdminId);
    LoginContextHolder.setLoginUserTenantId(tenantId);
    return Boolean.TRUE;
  }
}
