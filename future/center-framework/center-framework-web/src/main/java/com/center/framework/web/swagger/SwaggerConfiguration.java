package com.center.framework.web.swagger;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.customizers.GlobalOpenApiCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;

/**
 * swagger configuration.
 */
@Configuration
public class SwaggerConfiguration {

  @Value("${spring.application.name}")
  private String appName;
  /**
   * Api info.
   *
   * @return api info.
   */
  @Bean
  OpenAPI apiInfo() {
    return new OpenAPI()
        .info(new Info().title(appName+" API")
            .description(appName+" API description")
            .version("v1.0.1"));
  }


}
