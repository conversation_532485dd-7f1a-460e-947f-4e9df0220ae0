package com.center.framework.web.interceptor;


import static com.center.framework.common.CommonConstants.ANONYMOUS_ID;
import static com.center.framework.web.pojo.CommonResult.error;

// import cn.hutool.extra.servlet.ServletUtil; // 暂时移除，因为HuTool可能仍使用javax.servlet
import cn.hutool.json.JSONUtil;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.web.jwt.JwtTokenProvider;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

@Component
public class LoginValidateInterceptor implements HandlerInterceptor {

  @Resource
  private JwtTokenProvider jwtTokenProvider;

  private Pattern[] specialPatterns;


  @Value("${metahuman.special-urls}")
  private String[] specialUrls;


  @Override
  public void afterCompletion(HttpServletRequest request,
      HttpServletResponse response, Object arg2, Exception arg3)
      throws Exception {
//    请求完成后，需要清除ThreadLocal中的数据
    LoginContextHolder.clear();
  }

  @Override
  public void postHandle(HttpServletRequest request,
      HttpServletResponse response, Object arg2, ModelAndView arg3)
      throws Exception {

  }

  @Override
  public boolean preHandle(HttpServletRequest request,
      HttpServletResponse response, Object arg2) throws Exception {

    //1.拦截获取token
    String token = jwtTokenProvider.resolveToken(request);
    if (token != null) {
      if(jwtTokenProvider.validateToken(token)){
        LoginContextHolder.setLoginUserId(jwtTokenProvider.getUserId(token));
        LoginContextHolder.setLoginUserTenantId(jwtTokenProvider.getTenantId(token));
        LoginContextHolder.setLoginUserDepartId(jwtTokenProvider.getDepartId(token));
        return true;
      }else {
        if(isSpecialUrl(request)){
          return true;
        }
//      使用过期的token访问了需要认证的接
      }
    }else {
//      token为空，需要查检此uri是否支持特殊的：带token和不带token都可以访问
      if(isSpecialUrl(request)){
        return true;
      }
      //      未带token访问了需要认证的接口
    }
    response.setContentType(MediaType.APPLICATION_JSON_VALUE);
    response.setCharacterEncoding("UTF-8");
    try {
      response.getWriter().write(JSONUtil.toJsonStr(error(GlobalErrorCodeConstants.UNAUTHORIZED)));
    } catch (Exception e) {
      // 处理异常
    }
    return false;
  }

  private boolean isSpecialUrl(HttpServletRequest request){
    String requestURI = request.getRequestURI();
    String path = requestURI.substring(request.getContextPath().length());
    for (Pattern pattern : getSpecialPatterns()) {
      if (pattern.matcher(path).matches()) {
//          对于此类接口，设置一个默认的context（在后面操作数据库时，能获取tanentId,userId,departId），表示匿名访问
        LoginContextHolder.setLoginUserId(ANONYMOUS_ID);
        LoginContextHolder.setLoginUserTenantId(ANONYMOUS_ID);
        LoginContextHolder.setLoginUserDepartId(ANONYMOUS_ID);
        // 如果请求路径匹配排除模式，则不执行拦截器逻辑
        return true;
      }
    }
    return false;
  }

  private Pattern[] getSpecialPatterns() {
    if(null == specialPatterns){
      this.specialPatterns = new Pattern[specialUrls.length];
      for (int i = 0; i < specialUrls.length; i++) {
        this.specialPatterns[i] = Pattern.compile(specialUrls[i]);
      }
    }
    return specialPatterns;
  }

}
