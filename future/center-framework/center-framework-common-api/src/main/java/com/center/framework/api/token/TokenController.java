package com.center.framework.api.token;


import com.center.framework.web.jwt.JwtTokenProvider;
import com.center.framework.web.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

@Tag(name = "Token相关接口")
@RestController
@RequestMapping("/token")
public class TokenController {

    @Resource
    private JwtTokenProvider jwtTokenProvider;

    @Operation(summary = "检查Token是否合法")
    @Parameter(description = "token值")
    @GetMapping("/validate/{token}")
    public CommonResult<Boolean> validate(@PathVariable(value = "token") String token){
        if(jwtTokenProvider.validateToken(token)){
            return CommonResult.success(Boolean.TRUE);
        }
        return CommonResult.success(Boolean.FALSE);
    }
}
