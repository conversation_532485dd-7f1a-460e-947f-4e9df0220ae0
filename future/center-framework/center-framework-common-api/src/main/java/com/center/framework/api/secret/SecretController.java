package com.center.framework.api.secret;

import com.center.framework.common.utils.secret.SecretTool;
import com.center.framework.web.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@Tag(name = "加密解密接口")
@RestController
@RequestMapping("/secret")
public class SecretController {

    @PostMapping("/encrypt")
    @Operation(summary = "加密，需要前端传入纯文本(例子：curl -X POST http://localhost:9082/secret/encrypt -H \"Content-Type: text/plain\" -d singleParamValue (只需要value本身，无须key和括号引号,非json格式))")
    public CommonResult<String> encrypt(@RequestBody @Valid String content) {
        // 创建 SecretTool 实例
        SecretTool secretTool = new SecretTool();
        // 返回加密后的字符串
        return CommonResult.success(secretTool.encryptHex(content));
    }

    @PostMapping("/decrypt")
    @Operation(summary = "解密，需要前端传入纯文本(例子：curl -X POST http://localhost:9082/secret/decrypt -H \"Content-Type: text/plain\" -d singleParamValue (只需要value本身，无须key和括号引号,非json格式))")
    public CommonResult<String> decrypt(@RequestBody @Valid String content) {
        // 创建 SecretTool 实例
        SecretTool secretTool = new SecretTool();
        // 对密文字节数组进行解密，得到明文字符串
        return CommonResult.success(secretTool.decryptStrHex(content));
    }
}
