<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>center-framework</artifactId>
        <groupId>com.center</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>center-framework-short-message</artifactId>

    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
    </properties>

    <dependencies>
        <!--阿里云短信服务-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dysmsapi20170525</artifactId>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-web</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-db</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-check-message</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-jpa</artifactId>
            <classifier>jakarta</classifier>
            <version>5.1.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.querydsl</groupId>-->
<!--            <artifactId>querydsl-apt</artifactId>-->
<!--            <classifier>jakarta</classifier>-->
<!--            <version>5.1.0</version>-->
<!--        </dependency>-->
    </dependencies>

</project>