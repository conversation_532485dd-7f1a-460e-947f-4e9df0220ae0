package com.center.sms.persistence;

import com.center.framework.db.core.BaseModel;
import com.center.sms.common.SmsStatus;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;

import jakarta.persistence.*;

@Data
@Entity
@QueryEntity
@Table(name = "center_short_message")
public class SmsModel extends BaseModel {

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "sms_code")
    private String smsCode;

    @Column(name = "content")
    private String content;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private SmsStatus status;
}
