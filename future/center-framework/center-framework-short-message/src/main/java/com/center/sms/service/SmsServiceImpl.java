package com.center.sms.service;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.tea.TeaException;
import com.center.check.service.CheckService;
import com.center.framework.common.exception.ServerException;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.sms.common.SmsStatus;
import com.center.sms.persistence.SmsModel;
import com.center.sms.persistence.SmsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

@Service
@Slf4j
public class SmsServiceImpl implements SmsService {


    @Resource
    private SmsRepository smsRepository;

    @Resource
    private Client client;

    @Resource
    private CheckService checkService;

    @Value("${sms.template-code}")
    private String templateCode;

    @Value("${sms.sign-name}")
    private String signName;

    @Override
    public void verifyCode(String code, String phoneNumber) {
        SmsModel smsModel = smsRepository.findSmsModelBySmsCodeAndPhoneNumber(code,phoneNumber);
        if(null != smsModel){
            if(SmsStatus.USED.equals(smsModel.getStatus())){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INACTIVE_OBJECT,"短信验证码已被使用，请重新获取验证码！");
            }
            if(smsModel.getCreateTime().plusMinutes(5).isBefore(LocalDateTime.now())){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INACTIVE_OBJECT,"短信验证码已过期，请重新获取验证码！");
            }
        }else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"短信验证码不存在！");
        }
        smsModel.setStatus(SmsStatus.USED);
        smsRepository.save(smsModel);
    }

    @Override
    public void generateCode(String phoneNumber,String codeId,String verifyCode) {
        // 图片验证码逻辑
        checkService.validateCaptcha(codeId, verifyCode);

        if(!Validator.isMobile(phoneNumber)){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"手机号码不正确！");
        }
        String code = RandomUtil.randomNumbers(6);
        com.aliyun.dysmsapi20170525.models.SendSmsRequest sendSmsRequest = new com.aliyun.dysmsapi20170525.models.SendSmsRequest()
                .setPhoneNumbers(phoneNumber)
                .setSignName(signName)
                .setTemplateCode(templateCode)
                .setTemplateParam("{\"code\":\"" + code+ "\"}");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        SendSmsResponse response = null;
        try {
            response = client.sendSmsWithOptions(sendSmsRequest, runtime);
        } catch (TeaException error) {
            log.error("短信发送失败！",error);
            // 诊断地址
            log.error("Recommend",error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,"系统正忙，请稍后再试");
        } catch (Exception _error) {
            log.error("短信发送失败！",_error);
            TeaException error = new TeaException(_error.getMessage(), _error);
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,"系统正忙，请稍后再试");
        }
        if(!"OK".equals(response.getBody().getCode())){
            String body = JSONUtil.toJsonStr(response.getBody());
            log.error("短信发送失败"+ body);
            throw new ServerException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(),"系统正忙，请稍后再试",body);
        }
        SmsModel smsModel = new SmsModel();
        smsModel.setSmsCode(code);
        smsModel.setContent("");
        smsModel.setStatus(SmsStatus.UNUSED);
        smsModel.setPhoneNumber(phoneNumber);
        smsRepository.save(smsModel);
    }
}
