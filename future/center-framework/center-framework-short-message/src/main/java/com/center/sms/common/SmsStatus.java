package com.center.sms.common;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum SmsStatus implements IEnumerate<String> {

    USED("used","已使用"),
    UNUSED("unused","未使用");

    private String value;

    private String description;

    @Override
    public String getValue() {
        return null;
    }

    @Override
    public String getDescription() {
        return null;
    }
}
