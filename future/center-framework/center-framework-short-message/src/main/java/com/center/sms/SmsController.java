package com.center.sms;

import com.center.framework.web.pojo.CommonResult;
import com.center.sms.service.SmsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

@Tag(name = "手机短信验证码")
@RestController
@RequestMapping("/sms")
@Validated
public class SmsController {

    @Resource
    private SmsService smsService;

    @GetMapping(path = "/generate")
    @Operation(summary = "根据手机号生成验证码")
    @Parameter(name = "phone_number",description = "权限ID",example = "1")
    public CommonResult<String> generateCode(@RequestParam(name = "phone_number") String phoneNumber,@RequestParam String codeId, @RequestParam String verifyCode){
        smsService.generateCode(phoneNumber,codeId,verifyCode);
        return CommonResult.success();
    }

}
