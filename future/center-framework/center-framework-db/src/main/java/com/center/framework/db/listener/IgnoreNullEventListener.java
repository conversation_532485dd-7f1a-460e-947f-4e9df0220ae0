package com.center.framework.db.listener;

import com.center.framework.db.annotation.NotIgnoreNullField;
import java.util.Map;
import org.hibernate.bytecode.enhance.spi.LazyPropertyInitializer;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.event.internal.DefaultMergeEventListener;
import org.hibernate.persister.entity.EntityPersister;
import org.hibernate.property.access.internal.PropertyAccessStrategyBackRefImpl;
import org.hibernate.type.Type;

/**
 * 一个自定义监听器,负责监听在Hibernate在更新时的copyValue动作,如果源对象(entity)中的属于值为null,则不更新到目标对象(target)中
 * 在更新操作中,对没有赋值的属性不进行更新(避免更新为null)
 * 如果需要将字符型字段清空,则直接赋值为空(""),如果要把其他类型的字段清空,则在此对象上添加自定义注解@NotIgnoreNullField
 */

public class IgnoreNullEventListener extends DefaultMergeEventListener {

  public static final IgnoreNullEventListener INSTANCE = new IgnoreNullEventListener();

  @Override
  protected void copyValues(EntityPersister persistent, Object entity, Object target,
      SessionImplementor source, org.hibernate.event.spi.MergeContext copyCache) {
    if (target.getClass().getAnnotation(NotIgnoreNullField.class) != null) {
      //    如果此对象有@NotIgnoreNullField这个注解，使用原有逻辑（将Null属性的字段值清空）。
      super.copyValues(persistent, entity, target, source, copyCache);
    } else {
      //源目标
      Object[] original = persistent.getPropertyValues(entity);
      //存储目标
      Object[] targets = persistent.getPropertyValues(target);
      Type[] types = persistent.getPropertyTypes();
      Object[] copied = new Object[original.length];
      int len = types.length;
      for (int i = 0; i < len; i++) {
        if (original[i] == null ||
            original[i] == LazyPropertyInitializer.UNFETCHED_PROPERTY ||
            original[i] == PropertyAccessStrategyBackRefImpl.UNKNOWN
        ) {
          copied[i] = targets[i];
        } else {
          copied[i] = types[i].replace(original[i], targets[i], source, target, copyCache);
        }
      }

      persistent.setPropertyValues(target, copied);
    }
  }
}
