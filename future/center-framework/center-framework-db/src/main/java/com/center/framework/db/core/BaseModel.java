package com.center.framework.db.core;

import com.center.framework.db.annotation.GenerateId;
import com.center.framework.db.listener.BaseModeListener;
import java.time.LocalDateTime;
import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Data
@MappedSuperclass
@EntityListeners({AuditingEntityListener.class, BaseModeListener.class})
public abstract class BaseModel<T> {

  @Id
  @Column(name = "id", nullable = false, updatable = false)
//  自定义注解，自动生成Id @BaseModeListener
  @GenerateId
  protected Long id;

  @Column(name = "create_time")
  @CreatedDate
  protected LocalDateTime createTime;

  @Column(name = "update_time")
  @LastModifiedDate
  protected LocalDateTime updateTime;


  @Column(name = "creator_id")
  @CreatedBy
  protected Long creatorId;

  @Column(name = "updater_id")
  @LastModifiedBy
  protected Long updaterId;
}
