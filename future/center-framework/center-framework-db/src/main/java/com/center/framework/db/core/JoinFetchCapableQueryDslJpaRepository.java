package com.center.framework.db.core;

import java.io.Serializable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.NoRepositoryBean;

/**
 * QueryDsl join fetch capable repository.
 *
 * @param <T> Entity class.
 * @param <ID> ID.
 */
@SuppressWarnings({"InterfaceTypeParameterName"})
@NoRepositoryBean
public interface JoinFetchCapableQueryDslJpaRepository<T, ID extends Serializable>
    extends JpaRepository<T, ID>, QuerydslPredicateExecutor<T> {
}
