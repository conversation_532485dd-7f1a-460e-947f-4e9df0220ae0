package com.center.framework.db.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义的Annotation,通过Listener@BaseTenantModelListener监听@BaseTenantModel,
 * 在创建时，自动将当前登录用户的TenantId设置到BaseTenantModel的tenantId字段中
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface TenantId {

}
