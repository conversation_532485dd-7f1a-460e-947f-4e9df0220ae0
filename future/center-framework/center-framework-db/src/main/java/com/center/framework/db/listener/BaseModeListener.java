package com.center.framework.db.listener;

import cn.hutool.core.lang.Snowflake;
import com.center.framework.db.annotation.GenerateId;
import java.lang.reflect.Field;
import jakarta.annotation.Resource;
import jakarta.persistence.PrePersist;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.stereotype.Component;

/**
 * JPA对对象监听器，如果字段上指定了@GenerateId这个注解，
 * 则在对象保存进数据库前，系统自动给ID字段赋值
 */

@Component
@Slf4j
public class BaseModeListener {

  @Resource
  private Snowflake snowflake;

  @PrePersist
  public void PrePersist(final Object object){
    Class clazz = object.getClass();
    while (true){
      if(setIdValue(clazz.getDeclaredFields(),object)){
        break;
      }
      if(clazz.getSuperclass() != null){
        clazz = clazz.getSuperclass();
      }else {
        log.error("此对象添加了@BaseModelListener,但没有配置@GenerateId注解。"+object.getClass());
        break;
      }
    }

  }

  private boolean setIdValue(Field[] fields,Object object){
    for(Field field:fields){
      if(field.isAnnotationPresent(GenerateId.class)){
        BeanWrapper wrapper = new BeanWrapperImpl(object);
        if(wrapper.getPropertyValue(field.getName()) == null){
          wrapper.setPropertyValue(field.getName(), snowflake.nextId());
        }
        return true;
      }
    }
    return false;
  }

}
