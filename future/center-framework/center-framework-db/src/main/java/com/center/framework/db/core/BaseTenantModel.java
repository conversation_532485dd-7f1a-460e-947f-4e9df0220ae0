package com.center.framework.db.core;

import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;

import com.center.framework.db.annotation.TenantId;
import com.center.framework.db.listener.BaseTenantModeListener;
import lombok.Data;

@Data
@MappedSuperclass
@EntityListeners({BaseTenantModeListener.class})
public abstract class BaseTenantModel<T> extends BaseModel<T> {

  @TenantId
  @Column(name = "tenant_id",nullable = false)
  Long tenantId;
}
