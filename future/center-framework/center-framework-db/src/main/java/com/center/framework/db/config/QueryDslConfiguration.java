package com.center.framework.db.config;

import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.sql.JPASQLQuery;
import com.querydsl.sql.MySQLTemplates;
import com.querydsl.sql.PostgreSQLTemplates;
import com.querydsl.sql.SQLQueryFactory;
import com.querydsl.sql.SQLTemplates;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@EnableJpaAuditing
public class QueryDslConfiguration {

  @Value("${spring.datasource.url}")
  private String jdbUrl;

  @PersistenceContext
  private EntityManager entityManager;

  @Bean
  public JPAQueryFactory jpaQueryFactory() {
    return new JPAQueryFactory(entityManager);
  }

  @Bean
  public SQLQueryFactory sqlQueryFactory(DataSource dataSource) {
    SQLTemplates sqlTemplates = SQLTemplates.DEFAULT;
    if(jdbUrl.startsWith("jdbc:mysql")){
      sqlTemplates = MySQLTemplates.DEFAULT;
    }else if(jdbUrl.startsWith("jdbc:postgresql")){
      sqlTemplates = PostgreSQLTemplates.DEFAULT;
    }
    return new SQLQueryFactory(new com.querydsl.sql.Configuration(sqlTemplates), dataSource);
  }

  @Bean
  public JPASQLQuery jpasqlQuery(){
    SQLTemplates sqlTemplates = SQLTemplates.DEFAULT;
    if(jdbUrl.startsWith("jdbc:mysql")){
      sqlTemplates = MySQLTemplates.DEFAULT;
    }else if(jdbUrl.startsWith("jdbc:postgresql")){
      sqlTemplates = PostgreSQLTemplates.DEFAULT;
    }
    return new JPASQLQuery(entityManager, new com.querydsl.sql.Configuration(sqlTemplates));
  }

}
