<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.center</groupId>
        <artifactId>center-framework-storage</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>center-framework-storage-hdfs</artifactId>
    <packaging>jar</packaging>
    <description>center-framework-storage-hdfs</description>

    <dependencies>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-storage-interface</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- HDFS client dependency -->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
        </dependency>
    </dependencies>

</project>
