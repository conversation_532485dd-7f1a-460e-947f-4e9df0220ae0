package com.center.framework.storage.interfaces.pojo;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件元数据类，存储文件的基本信息。
 */
public class FileMetadata {
    // 文件名称
    private String fileName;
    // 文件大小（字节）
    private long size;
    // 最后修改时间（毫秒时间戳）
    private long lastModified;
    // 文件路径
    private String path;
    // 自定义属性
    private Map<String, String> customAttributes;

    /**
     * 构造方法，初始化文件元数据。
     *
     * @param fileName     文件名称
     * @param size         文件大小
     * @param lastModified 最后修改时间
     * @param path         文件路径
     */
    public FileMetadata(String fileName, long size, long lastModified, String path) {
        if (fileName == null || path == null) {
            throw new IllegalArgumentException("文件名称和路径不能为空");
        }
        this.fileName = fileName;
        this.size = size;
        this.lastModified = lastModified;
        this.path = path;
        this.customAttributes = new HashMap<>();
    }

    // Getter 和 Setter 方法

    public String getFileName() {
        return fileName;
    }

    public long getSize() {
        return size;
    }

    public long getLastModified() {
        return lastModified;
    }

    public String getPath() {
        return path;
    }

    public Map<String, String> getCustomAttributes() {
        return customAttributes;
    }

    // 自定义属性的添加和获取

    /**
     * 添加自定义属性。
     *
     * @param key   属性键
     * @param value 属性值
     */
    public void addCustomAttribute(String key, String value) {
        this.customAttributes.put(key, value);
    }

    /**
     * 获取自定义属性。
     *
     * @param key 属性键
     * @return 属性值
     */
    public String getCustomAttribute(String key) {
        return this.customAttributes.get(key);
    }
}
