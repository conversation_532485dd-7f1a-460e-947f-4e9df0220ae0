package com.center.framework.storage.interfaces.config;

import com.center.framework.storage.interfaces.enums.FileStorageTypeEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 文件存储配置类，用于从配置文件中读取存储类型。
 */
@Component
public class FileStorageConfig {

    private final FileStorageTypeEnum storageType;

    /**
     * 构造方法，读取配置文件中的存储类型。
     *
     * @param storageTypeString 配置文件中的存储类型字符串
     */
    public FileStorageConfig(@Value("${storage.type:}") String storageTypeString) { // 注意加了 ":"
        if (storageTypeString == null || storageTypeString.trim().isEmpty()) {
            // 配置为空，代表无存储服务，允许
            this.storageType = null;
            return;
        }
        try {
            this.storageType = FileStorageTypeEnum.valueOf(storageTypeString.trim().toUpperCase());
        } catch (IllegalArgumentException ex) {
            throw new IllegalArgumentException("存储类型配置错误，请确认 storage.type 配置项，允许值：" +
                    "[HDFS, MINIO, FTP, SFTP, OSS]，当前配置为：" + storageTypeString);
        }
    }

    public FileStorageTypeEnum getStorageType() {
        return storageType;
    }

    public boolean isStorageEnabled() {
        return this.storageType != null;
    }
}

