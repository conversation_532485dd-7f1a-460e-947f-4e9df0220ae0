package com.center.cache.memory;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.CacheObj;
import cn.hutool.cache.impl.TimedCache;
import com.center.cache.interfaces.Cache;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description: string实现类
 * @date 2024/11/12 15:24
 */
@Component
public class MemoryStrCache implements Cache<String, String> {

    // 由于 hutool 的 cache 不支持不限时间并且不限数量的缓存，所以这里设定一个长时间（100年）的缓存
    private cn.hutool.cache.Cache<String, cn.hutool.cache.Cache<String, String>> stringCache =
            CacheUtil.newTimedCache(3155760000000L);

    @Override
    public String get(String key) {
        cn.hutool.cache.Cache<String, String> cache = stringCache.get(key);
        if (cache == null) {
            return null;
        }
        return cache.get(key);
    }

    @Override
    public void put(String key, String value) {
        // 模仿 redis 逻辑，给每一个 key 设置一个 cache，每个 cache 实际只存储一条数据
        cn.hutool.cache.Cache<String, String> cache = CacheUtil.newLRUCache(100);
        cache.put(key, value);
        stringCache.put(key, cache);
    }

    @Override
    public void put(String key, String value, Long ttlTime) {
        // 模仿 redis 逻辑，给每一个 key 设置一个 cache，每个 cache 实际只存储一条数据
        cn.hutool.cache.Cache<String, String> cache = CacheUtil.newTimedCache(ttlTime);
        cache.put(key, value);
        stringCache.put(key, cache);
    }

    @Override
    public void put(String key, String value, long time, TimeUnit unit) {
        // 将时间单位转换为毫秒后调用带 Long ttlTime 参数的 put 方法
        long ttlTimeMillis = unit.toMillis(time);
        put(key, value, ttlTimeMillis);
    }

    @Override
    public void remove(String key) {
        cn.hutool.cache.Cache<String, String> cache = stringCache.get(key);
        if (cache != null) {
            cache.remove(key);
            stringCache.remove(key);
        }
    }

    @Override
    public Set<String> keys(String pattern) {
        Set<String> result = new HashSet<>();
        if (pattern == null || pattern.isEmpty()) {
            return result;
        }
        // 将通配符表达式转换为正则表达式
        String regex = pattern.replace("*", ".*").replace("?", ".");
        Pattern compiledPattern = Pattern.compile(regex);

        // 如果 stringCache 为 TimedCache 类型，则使用 cacheObjIterator() 遍历所有键
        if (stringCache instanceof TimedCache) {
            TimedCache<String, cn.hutool.cache.Cache<String, String>> timedCache =
                    (TimedCache<String, cn.hutool.cache.Cache<String, String>>) stringCache;
            Iterator<CacheObj<String, cn.hutool.cache.Cache<String, String>>> iterator = timedCache.cacheObjIterator();
            while (iterator.hasNext()) {
                CacheObj<String, cn.hutool.cache.Cache<String, String>> cacheObj = iterator.next();
                String key = cacheObj.getKey();
                if (compiledPattern.matcher(key).matches()) {
                    result.add(key);
                }
            }
        }
        return result;
    }
}
