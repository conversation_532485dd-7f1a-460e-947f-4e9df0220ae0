package com.center.cache.memory;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.CacheObj;
import cn.hutool.cache.impl.TimedCache;
import com.center.cache.interfaces.Cache;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description: set实现类
 * @date 2024/11/13 9:11
 */
@Component
public class MemorySetCache implements Cache<String, Set<String>> {

    //由于hutool的cache不支持不限时间并且不限数量的缓存，所以这里设定一个长时间（100年）的缓存
    private cn.hutool.cache.Cache<String, cn.hutool.cache.Cache<String, Set<String>>> setCache = CacheUtil.newTimedCache(3155760000000L);


    @Override
    public Set<String> get(String key) {
        cn.hutool.cache.Cache<String, Set<String>> cache = setCache.get(key);
        if (cache == null) {
            return null;
        }
        return cache.get(key);
    }

    @Override
    public void put(String key, Set<String> value) {
        //这里是模仿redis逻辑，给每一个key设置一个cache，也就是每一个cache实际只会有一条内容
        cn.hutool.cache.Cache<String, Set<String>> cache = CacheUtil.newLRUCache(100);
        cache.put(key, value);
        setCache.put(key, cache);
    }

    @Override
    public void put(String key, Set<String> value, Long ttlTime) {
        //这里是模仿redis逻辑，给每一个key设置一个cache，也就是每一个cache实际只会有一条内容
        cn.hutool.cache.Cache<String, Set<String>> cache = CacheUtil.newTimedCache(ttlTime);
        cache.put(key, value);
        setCache.put(key, cache);
    }
    
    @Override
    public void put(String key, Set<String> value, long time, TimeUnit unit) {
        // 将时间单位转换为毫秒
        long ttlTimeMillis = unit.toMillis(time);
        put(key, value, ttlTimeMillis);
    }

    @Override
    public void remove(String key) {
        cn.hutool.cache.Cache<String, Set<String>> cache = setCache.get(key);
        if(cache != null){
            cache.remove(key);
            setCache.remove(key);
        }
    }


    @Override
    public Set<String> keys(String pattern) {
        Set<String> result = new HashSet<>();
        if (pattern == null || pattern.isEmpty()) {
            return result;
        }

        // 将通配符表达式转换为正则表达式
        String regex = pattern.replace("*", ".*").replace("?", ".");
        Pattern compiledPattern = Pattern.compile(regex);

        // 如果 setCache 为 TimedCache 类型，则使用 cacheObjIterator() 方法遍历所有键
        if (setCache instanceof TimedCache) {
            TimedCache<String, cn.hutool.cache.Cache<String, Set<String>>> timedCache =
                    (TimedCache<String, cn.hutool.cache.Cache<String, Set<String>>>) setCache;
            Iterator<CacheObj<String, cn.hutool.cache.Cache<String, Set<String>>>> iterator = timedCache.cacheObjIterator();
            while (iterator.hasNext()) {
                CacheObj<String, cn.hutool.cache.Cache<String, Set<String>>> cacheObj = iterator.next();
                String key = cacheObj.getKey();
                if (compiledPattern.matcher(key).matches()) {
                    result.add(key);
                }
            }
        }
        return result;
    }
}
