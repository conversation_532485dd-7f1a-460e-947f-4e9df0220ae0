package com.center.cache.interfaces;

import java.util.Set;
import java.util.concurrent.TimeUnit;

public interface Cache<K, V> {
    V get(K key);

    void put(K key, V value);

    void put(K key, V value, Long ttlTime);
    
    /**
     * 存储键值对，并设置过期时间
     * @param key 键
     * @param value 值
     * @param time 过期时间
     * @param unit 时间单位
     */
    void put(K key, V value, long time, TimeUnit unit);

    void remove(K key);
    
    /**
     * 获取匹配模式的所有键
     * @param pattern 匹配模式
     * @return 匹配的键集合
     */
    Set<K> keys(String pattern);
}
