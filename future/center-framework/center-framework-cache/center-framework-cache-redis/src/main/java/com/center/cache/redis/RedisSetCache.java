package com.center.cache.redis;

import com.center.cache.interfaces.Cache;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: redis set缓存实现类
 * @date 2024/11/13 9:28
 */
@Component
public class RedisSetCache implements Cache<String, Set<String>> {

    @Resource
    @Lazy
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public Set<String> get(String key) {
        Set<String> value = redisTemplate.opsForSet().members(key);
        return (value == null || value.isEmpty()) ? null : value;
    }

    @Override
    public void put(String key, Set<String> value) {
        if (value != null && !value.isEmpty()) {
            // 将 set 中的每个成员依次添加到 Redis 中
            redisTemplate.opsForSet().add(key, value.toArray(new String[0]));
        }
    }

    @Override
    public void put(String key, Set<String> value, Long ttlTime) {
        if (value != null && !value.isEmpty()) {
            redisTemplate.opsForSet().add(key, value.toArray(new String[0]));
            redisTemplate.expire(key, ttlTime, TimeUnit.MILLISECONDS);
        }
    }

    @Override
    public void put(String key, Set<String> value, long time, TimeUnit unit) {
        if (value != null && !value.isEmpty()) {
            redisTemplate.opsForSet().add(key, value.toArray(new String[0]));
            redisTemplate.expire(key, time, unit);
        }
    }

    @Override
    public void remove(String key) {
        redisTemplate.delete(key);
    }

    @Override
    public Set<String> keys(String pattern) {
        Set<String> keys = redisTemplate.keys(pattern);
        return keys != null ? keys : Collections.emptySet();
    }
}
