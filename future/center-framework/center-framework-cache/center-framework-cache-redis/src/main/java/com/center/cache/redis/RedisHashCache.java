package com.center.cache.redis;

import com.center.cache.interfaces.Cache;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: redis hash缓存实现类
 * @date 2024/11/13 9:54
 */
@Component
public class RedisHashCache implements Cache<String, Object> {

    @Resource
    @Lazy
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Object get(String key) {
        return redisTemplate.opsForHash().get(key, key);
    }

    @Override
    public void put(String key, Object value) {
        redisTemplate.opsForHash().put(key, key, value);
    }

    @Override
    public void put(String key, Object value, Long ttlTime) {
        redisTemplate.opsForHash().put(key, key, value);
        redisTemplate.expire(key, ttlTime, TimeUnit.MILLISECONDS);
    }

    @Override
    public void put(String key, Object value, long time, TimeUnit unit) {
        // 直接调用 RedisTemplate 的 expire 方法
        redisTemplate.opsForHash().put(key, key, value);
        redisTemplate.expire(key, time, unit);
    }

    @Override
    public void remove(String key) {
        redisTemplate.delete(key);
    }

    @Override
    public Set<String> keys(String pattern) {
        // 使用 RedisTemplate 的 keys 方法获取匹配的 key 集合
        Set<String> keys = redisTemplate.keys(pattern);
        return keys != null ? keys : Collections.emptySet();
    }
}
