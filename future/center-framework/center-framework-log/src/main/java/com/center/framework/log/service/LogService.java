package com.center.framework.log.service;

import java.util.List;

public interface LogService {
    /**
     * 将传入的日志信息写入本地文件，文件路径按照“日期-IP.log”格式命名，并以追加模式保存。
     *
     * 功能说明：
     * - 生成文件名格式为：yyyy-MM-dd-IP.log
     * - 日志文件保存在配置的 logPath/logs 目录下
     * - 支持多条日志追加写入，每条日志包含当前时间戳
     *
     * @param ip          日志来源的客户端 IP，用于区分日志文件
     * @param logMessages 日志内容列表，每条为一行，将按顺序写入日志文件中
     * @throws RuntimeException 如果在日志写入过程中发生 IO 错误，则抛出运行时异常
     */
    void recordLog(String ip, String message);
}
