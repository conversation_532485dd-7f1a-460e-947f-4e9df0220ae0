package com.center.framework.log.service;

import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
public class LogServiceImpl implements LogService {
    @Value("${nginx.logPath}")
    private String logPath;


    @Override
    public void recordLog(String ip, String message) {
        // 1. 根据日期和 IP 组成文件名
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Path targetDir = Paths.get(logPath, "logs", dateStr);

        // 2. 构建目标文件路径：safeIp.log
        Path targetFile = targetDir.resolve(sanitizeIp(ip) + ".log");

        try {
            // 3. 创建目录（如果不存在）
            if (!Files.exists(targetDir)) {
                Files.createDirectories(targetDir);
            }

            // 4. 逐条写入日志（追加模式）
            try (BufferedWriter writer = Files.newBufferedWriter(
                    targetFile,
                    StandardOpenOption.CREATE,       // 文件不存在则创建
                    StandardOpenOption.WRITE,
                    StandardOpenOption.APPEND)) {    // 追加

                String timePrefix = LocalDateTime.now()
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                writer.write(timePrefix);
                writer.write(" - ");
                writer.write(message);
                writer.newLine();

            }
        } catch (IOException e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, "日志信息存储失败");
        }
    }

    // 安全化 IP，替换掉文件名非法字符
    private String sanitizeIp(String ip) {
        return ip.replace(":", ".");
    }

}
