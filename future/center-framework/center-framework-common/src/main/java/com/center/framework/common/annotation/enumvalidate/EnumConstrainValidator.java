package com.center.framework.common.annotation.enumvalidate;

import com.center.framework.common.enumerate.IEnumerate;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.concurrent.atomic.AtomicBoolean;

public class EnumConstrainValidator implements ConstraintValidator<EnumValidate,String> {

  private Class<? extends IEnumerate<?>> enumValue;

  @Override
  public void initialize(EnumValidate enumValidate) {
    enumValue = enumValidate.value();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
    AtomicBoolean atomicBoolean = new AtomicBoolean(true);
    if(null == value || null == enumValue || !enumValue.isEnum()){
      return atomicBoolean.get();
    }
    for(IEnumerate<?> IEnumerate : enumValue.getEnumConstants()){
      if(value.equals(IEnumerate.getValue())){
        return atomicBoolean.get();
      }
    }
    atomicBoolean.set(false);
    return atomicBoolean.get();
  }
}
