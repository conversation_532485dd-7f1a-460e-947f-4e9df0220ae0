package com.center.framework.common.utils.Tree;


import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.center.framework.common.CommonConstants;

import java.util.List;

public class TreeNodeUtils {


    /**
     * TreeNode对象中默认主键为ID（必须有），
     * 排序字段在TreeNode对象中的extra(Map对象)属性中定义，key为“sort”（可选字段）
     * 默认树递归的最大深度为6级
     * @param nodeList
     * @return
     */
    public static List<Tree<String>> buildTree(List<TreeNode> nodeList){

    //配置
    TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
    //设置权重对应的名称,用于排序
    treeNodeConfig.setWeightKey("sort");
    treeNodeConfig.setIdKey("id");
    treeNodeConfig.setNameKey("label");
// 最大递归深度
    treeNodeConfig.setDeep(6);//这个是设置树形结构的层级
    //转换器 (含义:找出父节点为字符串零的所有子节点, 并递归查找对应的子节点, 深度最多为 6)
    // 0表示最顶层的id是0,即最高的父级id为多少
    List<Tree<String>> build = TreeUtil.<TreeNode, String>build(nodeList,
            String.valueOf(CommonConstants.OBJECT_ROOT_ID), treeNodeConfig,
            (treeNode, tree) -> {
                tree.setId(String.valueOf(treeNode.getId()));
                tree.setParentId(String.valueOf(treeNode.getParentId()));
                tree.setName(treeNode.getName());
                tree.putAll(treeNode.getExtra());
            });
    return build;
}
}
