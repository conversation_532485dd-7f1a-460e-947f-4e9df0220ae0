package com.center.framework.common.utils.json;

import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Json格式解析
 */
@Slf4j
public class ParseJsonUtils {

    /**
     * 将json字符串解析成Map
     * @param json
     * @return
     */
    public static Map parseJson(String json){
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> projectInfoMap=null;
        try {
            projectInfoMap = objectMapper.readValue(json, new TypeReference<Map<String, String>>() {});
        } catch (JsonProcessingException e) {
            log.info("无效的json格式", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "无效的json格式");
        }
        return projectInfoMap;
    }

    /**
     * 将json字符串解析成List
     * @param json
     * @return
     */
    public static List parseJsons(String json){
        ObjectMapper objectMapper = new ObjectMapper();
        List<Map<String, String>> projectInfoList = new ArrayList<>();
        try {
            projectInfoList = objectMapper.readValue(json, new TypeReference<List<Map<String, String>>>() {});
        } catch (JsonProcessingException e) {
            log.info("无效的json格式", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "无效的json格式");
        }
        return projectInfoList;
    }
}
