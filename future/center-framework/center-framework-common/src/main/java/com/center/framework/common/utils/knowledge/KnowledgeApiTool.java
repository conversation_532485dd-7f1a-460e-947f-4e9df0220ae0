package com.center.framework.common.utils.knowledge;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 通用的工具类，用于调用知识库相关的API
 */
@Slf4j
public class KnowledgeApiTool {

    private static final int CONNECT_TIMEOUT = 60;  // 连接超时时间（秒）
    private static final int READ_TIMEOUT = 60;     // 读取超时时间（秒）
    private static final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .build();

    /**
     * 发送POST请求，并返回JSON响应结果
     * @param url API的URL
     * @param requestBody 请求体数据（JSON格式）
     * @return JSONObject 解析后的JSON对象
     * @throws Exception 可能出现的异常
     */
    public static JSONObject post(String url, Map<String, Object> requestBody) throws Exception {
        Map<String, Object> logRequestBody = new LinkedHashMap<>(requestBody);
        if (logRequestBody.containsKey("pdf_text")) {
            logRequestBody.put("pdf_text", "This is a long PDF-base64 content");
        }
        // 将请求体转换为JSON格式
        String jsonBody = JSON.toJSONString(requestBody);
        log.info("发送POST请求: {}, 请求体: {}", url, logRequestBody);

        // 创建请求体
        RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json; charset=utf-8"));

        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            // 检查响应是否成功
            if (!response.isSuccessful()) {
                log.error("API请求失败, 状态码: {}, 响应: {}", response.code(), response.body().string());
                throw new IOException("API请求失败，状态码: " + response.code());
            }

            // 获取响应体字符串
            String responseStr = response.body().string();
            log.info("API响应成功, 响应内容: {}", responseStr);

            // 将响应字符串解析为JSON对象
            return JSON.parseObject(responseStr);
        } catch (IOException e) {
            log.error("API请求出现异常: {}", e.getMessage());
            throw new RuntimeException("调用API失败", e);
        }
    }

    public static JSONArray post2(String url, Map<String, Object> requestBody) throws Exception {
        Map<String, Object> logRequestBody = requestBody;
        if (logRequestBody.containsKey("pdf_text")) {
            logRequestBody.put("pdf_text", "This is a long PDF-base64 content");
        }
        // 将请求体转换为JSON格式
        String jsonBody = JSON.toJSONString(requestBody);
        log.info("发送POST请求: {}, 请求体: {}", url, logRequestBody);

        // 创建请求体
        RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json; charset=utf-8"));
        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        try (Response response = httpClient.newCall(request).execute()) {
            // 检查响应是否成功
            if (!response.isSuccessful()) {
                log.error("API请求失败, 状态码: {}, 响应: {}", response.code(), response.body().string());
                throw new IOException("API请求失败，状态码: " + response.code());
            }
            // 可以根据 response 判断是否有错误并处理
            if (response.code() != 200) {
                String errorMsg = response.body() != null ? response.body().string() : "无错误信息";
                log.error("Python API 返回失败，错误信息: {}", errorMsg);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, errorMsg);
            }
            // 获取响应体字符串
            String responseStr = null;
            if (response.body() != null) {
                responseStr = response.body().string();
            }
            log.info("API响应成功, 响应内容: {}", responseStr);

            // 将响应字符串解析为JSON对象
            return JSON.parseArray(responseStr);
        } catch (IOException e) {
            log.error("API请求出现异常: {}", e.getMessage());
            throw new RuntimeException("调用API失败", e);
        }
    }
    /**
     * 发送GET请求，并返回JSON响应结果
     * @param url API的URL
     * @param params 请求参数
     * @return JSONObject 解析后的JSON对象
     * @throws Exception 可能出现的异常
     */
    public static JSONObject get(String url, Map<String, String> params) throws Exception {
        // 构建URL参数
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            urlBuilder.addQueryParameter(entry.getKey(), entry.getValue());
        }
        String finalUrl = urlBuilder.build().toString();
        log.info("发送GET请求: {}", finalUrl);

        // 构建请求
        Request request = new Request.Builder()
                .url(finalUrl)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            // 检查响应是否成功
            if (!response.isSuccessful()) {
                log.error("API请求失败, 状态码: {}, 响应: {}", response.code(), response.body().string());
                throw new IOException("API请求失败，状态码: " + response.code());
            }

            // 获取响应体字符串
            String responseStr = response.body().string();
            log.info("API响应成功, 响应内容: {}", responseStr);

            // 将响应字符串解析为JSON对象
            return JSON.parseObject(responseStr);
        } catch (IOException e) {
            log.error("API请求出现异常: {}", e.getMessage());
            throw new RuntimeException("调用API失败", e);
        }
    }

    /**
     * 从JSON响应中提取关键字段
     * @param jsonObject JSON响应
     * @param key 字段名
     * @return 字段值
     */
    public static String extractField(JSONObject jsonObject, String key) {
        if (jsonObject == null) {
            log.error("JSON对象为空，无法提取字段: {}", key);
            return null;
        }
        String value = jsonObject.getString(key);
        log.info("提取字段: {}, 值: {}", key, value);
        return value;
    }
}