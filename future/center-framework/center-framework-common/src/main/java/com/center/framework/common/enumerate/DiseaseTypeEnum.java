package com.center.framework.common.enumerate;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum DiseaseTypeEnum implements IEnumerate<String> {
    FAMILY_HISTORY("FAMILY_HISTORY", "家族病史"),
    PAST_HISTORY("PAST_HISTORY", "既往病史"),
    ALLERGY_HISTORY("ALLERGY_HISTORY", "过敏史");

    private String value;

    private String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
