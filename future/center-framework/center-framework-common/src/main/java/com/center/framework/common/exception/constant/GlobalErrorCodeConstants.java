package com.center.framework.common.exception.constant;


import com.center.framework.common.exception.ErrorCode;

/**
 * 全局错误码枚举
 * 0-999 系统异常编码保留
 *
 *
 */
public interface GlobalErrorCodeConstants {

    ErrorCode SUCCESS = new ErrorCode(2000, "成功");

    // ========== 客户端错误段 ==========

    ErrorCode BAD_REQUEST = new ErrorCode(4001, "请求错误。");
    ErrorCode FORBIDDEN = new ErrorCode(4002, "没有该操作权限。-{}");
    ErrorCode NOT_FOUND = new ErrorCode(4003, "请求未找到。");
    ErrorCode METHOD_NOT_ALLOWED = new ErrorCode(4004, "请求方法不正确。");
    ErrorCode PARAM_MISMATCH = new ErrorCode(4005, "请求参数不匹配。"); // 并发请求，不允许
    ErrorCode REQUEST_PARAM_ERROR = new ErrorCode(4006, "请求参数错误。-{}");

    //    通用错误代码
    ErrorCode UNAUTHORIZED = new ErrorCode(4101, "账号未登录。");
    ErrorCode INVALID_USERNAME_PASSWORD = new ErrorCode(4102, "用户名密码错误，或手机号未注册。");
    ErrorCode OBJECT_NOT_EXISTED = new ErrorCode(41003,ErrorMessage.OBJECT_NOT_EXISTED_MSG);
    ErrorCode DUPLICATED_OBJECT = new ErrorCode(41004,ErrorMessage.DUPLICATED_OBJECT);
    ErrorCode INACTIVE_OBJECT = new ErrorCode(41005,ErrorMessage.INACTIVE_OBJECT);
    ErrorCode OVER_LIMIT = new ErrorCode(41006,ErrorMessage.OVER_LIMIT);
    ErrorCode NOT_EMPTY = new ErrorCode(41007,ErrorMessage.NOT_EMPTY);
    ErrorCode INVALID_CONTENT = new ErrorCode(41008,"非法的内容-{}");
    ErrorCode GET_OBJECT_ERROR = new ErrorCode(41009,ErrorMessage.GET_ERROR);
    ErrorCode DELETE_OBJECT_ERROR = new ErrorCode(41010,"删除失败!-{}");
    ErrorCode UPLOAD_FILE_ERROR = new ErrorCode(41011,"未知错误导致上传文件异常，请联系管理员!");


    // ========== 服务端错误段 ==========

    ErrorCode INTERNAL_SERVER_ERROR = new ErrorCode(50000, "系统异常-{}。");
    ErrorCode OUTER_SERVER_EXCEPTION = new ErrorCode(50002, "服务调用异常-{}。");

    // ========== 自定义错误段 ==========
//   从60000开始，infrastructure模型使用60000~609999
    // ========== 数据库错误段 ==========
    ErrorCode DATABASE_UNKNOW_ERROR = new ErrorCode(60001, "数据库未知异常。");
    ErrorCode USERANDPASSWORD_ERROR = new ErrorCode(60002, "用户名或密码异常。");
    ErrorCode CONNECTION_ERROR = new ErrorCode(60003, "数据库连接异常。");
    ErrorCode CONNECTION_TIMEOUT_ERROR = new ErrorCode(60004, "数据库连接超时。");
    ErrorCode CLOSE_DATABASE_ERROR = new ErrorCode(60006, "关闭数据库异常。");
    ErrorCode DATABASE_NOT_SELECTED_ERROR = new ErrorCode(6009, "没有选择数据库。");
    ErrorCode CONNECTION_LOST_ERROR = new ErrorCode(60010, "数据库连接超时丢失。");
    ErrorCode CONNECTION_CLOSE_ERROR = new ErrorCode(60011, "数据库连接已关闭。");
    ErrorCode DRIVER_NOT_FOUND_ERROR = new ErrorCode(60012, "未找到驱动。");
    ErrorCode KAFKA_CLIENT_ERROR = new ErrorCode(60013, "Kafka客户端的配置错误。");
    ErrorCode DATA_NOT_FOUND_ERROR = new ErrorCode(60014, "数据查询失败-{}。");
    // ========== 数据表错误段 ==========
    ErrorCode SSL_UNABLE_ERROR = new ErrorCode(60031, "连接配置没有启用 TLS/SSL。");
    ErrorCode GET_CLIENT_ERROR = new ErrorCode(60032, "创建或使用 AdminClient 失败。");


//    ==================Dolphin Scheduler调用错误代码=============================
    ErrorCode DOLPHIN_SCHEDULER_RESPONSE_ERROR = new ErrorCode(61000, "调度引擎-{}。");
    //===================文件管理错误段=============================
    ErrorCode FILETYPE_ERROR = new ErrorCode(62001,"文件格式错误-{}。");
    ErrorCode FILENAME_EXIST_ERROR = new ErrorCode(62002,"文件名已存在w-{}。");
    ErrorCode IO_ERROR = new ErrorCode(62003,"I/O操作异常，可能由于网络问题或文件系统故障!-{}");
    ErrorCode CONNECTION_LOST = new ErrorCode(62004,"与SFTP服务器的连接丢失");
    ErrorCode SFTP_ERROR = new ErrorCode(62005,"其他SFTP异常");
    ErrorCode SOCKET_ERROR = new ErrorCode(62007,"网络连接问题，服务器不可达或连接超时!");
    ErrorCode UNKNOWN_HOST_ERROR = new ErrorCode(62008,"无法解析主机名!");
    ErrorCode EOF_ERROR = new ErrorCode(62009,"意外结束文件流，通常表示与服务器的连接丢失!");
    ErrorCode FILE_NOT_FOUND = new ErrorCode(62010,"指定路径不存在!");
    ErrorCode INTERRUPTED = new ErrorCode(62011,"操作被中断，可能是由于线程中断导致的!");
    ErrorCode PATH_CREATE_ERROR = new ErrorCode(62012,"文件夹创建失败!");
    /**
     * 是否为服务端错误，参考 HTTP 5XX 错误码段
     *
     * @param code 错误码
     * @return 是否
     */
   static boolean isServerErrorCode(Integer code) {
       return code != null
               && code >= INTERNAL_SERVER_ERROR.getCode() && code <= INTERNAL_SERVER_ERROR.getCode() + 99;
   }

}
