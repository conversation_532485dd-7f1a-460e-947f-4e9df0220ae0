package com.center.framework.common.utils.cron;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.center.framework.common.enumerate.JobEnum;
import lombok.Data;

public class CronVO {

//  用于Restful接口中的对象中，数据库中使用的字段名为day_of_week，为字符型（单独提供了getter，setter方法，用于对象复制）
  Integer[] dayOfWeeks;

  //  用于Restful接口中的对象中，数据库中使用的字段名为day_of_month，为字符型（单独提供了getter，setter方法，用于对象复制）
  Integer[] dayOfMonths;

  //  用于Restful接口中的对象中，数据库中使用的字段名为month，为字符型（单独提供了getter，setter方法，用于对象复制）
  Integer[] months;

  Integer second;

  Integer minute;

  Integer hour;

  /**
   * 类型
   */
  JobEnum jobType;
  /**
   * 间隔
   */
  Integer beApart;

  public Integer[] getDayOfWeeks() {
    return dayOfWeeks;
  }

  public void setDayOfWeeks(Integer[] dayOfWeeks) {
    this.dayOfWeeks = dayOfWeeks;
  }

  public Integer[] getDayOfMonths() {
    return dayOfMonths;
  }

  public void setDayOfMonths(Integer[] dayOfMonths) {
    this.dayOfMonths = dayOfMonths;
  }

  public Integer[] getMonths() {
    return months;
  }

  public void setMonths(Integer[] months) {
    this.months = months;
  }

  public Integer getSecond() {
    return second == null ? 0 : second;
  }

  public void setSecond(Integer second) {
    this.second = second;
  }

  public Integer getMinute() {
    return minute == null ? 0 : minute;
  }

  public void setMinute(Integer minute) {
    this.minute = minute;
  }

  public Integer getHour() {

    return hour==null ? 0 : hour;
  }

  public void setHour(Integer hour) {
    this.hour = hour;
  }

  public JobEnum getJobType() {
    return jobType == null ? JobEnum.DAY : jobType;
  }

  public void setJobType(JobEnum jobType) {
    this.jobType = jobType;
  }

  public Integer getBeApart() {
    return beApart;
  }

  public void setBeApart(Integer beApart) {
    this.beApart = beApart;
  }


//  Integer[] dayOfWeeks;
//
//  Integer[] dayOfMonths;
//
//  Integer[] months;
  public String getDayOfWeek(){
    return dayOfWeeks == null ? StrUtil.EMPTY : ArrayUtil.toString(dayOfWeeks);
  }

  public void setDayOfWeek(String dayOfWeek){
    if(StrUtil.isNotEmpty(dayOfWeek)){
      dayOfMonths = StrUtil.split(dayOfWeek,",").stream()
          .map(Convert::toInt)
          .toArray(Integer[]::new);;
    }
  }
  public String getDayOfMonth(){
    return dayOfMonths == null ? StrUtil.EMPTY : ArrayUtil.toString(dayOfMonths);
  }

  public void setDayOfMonth(String dayOfMonth){
    if(StrUtil.isNotEmpty(dayOfMonth)){
      dayOfMonths = StrUtil.split(dayOfMonth,",").stream()
          .map(Convert::toInt)
          .toArray(Integer[]::new);;
    }
  }
  public String getMonth(){
    return months == null ? StrUtil.EMPTY : ArrayUtil.toString(months);
  }

  public void setMonth(String month){
    if(StrUtil.isNotEmpty(month)){
      months = StrUtil.split(month,",").stream()
          .map(Convert::toInt)
          .toArray(Integer[]::new);;
    }
  }


}

