<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.center</groupId>
        <artifactId>center-infrastructure</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>center-infrastructure-system</artifactId>
    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-cache-factory</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-db</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-web</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-jpa</artifactId>
            <classifier>jakarta</classifier>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-apt</artifactId>
            <classifier>jakarta</classifier>
            <scope>provided</scope>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
        <!--如果Q类对象没有生成，可以用下面2个plugins中的一个-->
        <!--处理 Querydsl Q 类 生成 -->
<!--            <plugin>-->
<!--                <groupId>com.mysema.maven</groupId>-->
<!--                <artifactId>apt-maven-plugin</artifactId>-->
<!--                <version>${mysema.maven.version}</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <goals>-->
<!--                            <goal>process</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <outputDirectory>target/generated-sources/java</outputDirectory>-->
<!--                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->

            <!--            处理 Querydsl Q 类 生成 -->
<!--            <plugin>-->
<!--                <groupId>com.mysema.maven</groupId>-->
<!--                <artifactId>apt-maven-plugin</artifactId>-->
<!--                <version>${mysema.maven.version}</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <goals>-->
<!--                            <goal>process</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <outputDirectory>target/generated-sources/java</outputDirectory>-->
<!--                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>-->
<!--                            <options>-->
<!--                                <querydsl.entityAccessors>true</querydsl.entityAccessors>-->
<!--                                <querydsl.useFields>false</querydsl.useFields>-->
<!--                            </options>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <dependencies>-->
<!--                    <dependency>-->
<!--                        <groupId>com.querydsl</groupId>-->
<!--                        <artifactId>querydsl-apt</artifactId>-->
<!--                        <classifier>jakarta</classifier>-->
<!--                        <version>5.1.0</version>-->
<!--                    </dependency>-->
<!--                </dependencies>-->
<!--            </plugin>-->

<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <annotationProcessorPaths>-->
<!--                        <path>-->
<!--                            <groupId>org.mapstruct</groupId>-->
<!--                            <artifactId>mapstruct-processor</artifactId>-->
<!--                        </path>-->
<!--                    </annotationProcessorPaths>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>
</project>