package com.center.infrastructure.system.biz.user.service;

import com.center.infrastructure.system.biz.user.pojo.UserCreateReq;
import com.center.infrastructure.system.biz.user.pojo.UserResp;
import com.center.infrastructure.system.biz.user.pojo.UserRoleCreateReq;
import com.center.infrastructure.system.biz.user.pojo.UserRoleUpdateReq;
import com.center.infrastructure.system.biz.user.pojo.UserUpdateReq;

public interface UserService {

  UserResp get(Long id);

  void save(UserCreateReq userCreateReq);

  void update(UserUpdateReq userUpdateReq);

  void delete(Long id);

  void saveUserRole(UserRoleCreateReq userRoleCreateReq);

  void updateUserRole(UserRoleUpdateReq userRoleCreateReq);

  UserResp getByUsernameAndPassword(String username,String password);

  void updateUserLoginInfo(Long id,String ip);

}
