package com.center.infrastructure.system.biz.dict.persistence;

import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Table;

@Data
@Entity
@Table(name = "algo_object_dict")
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class ObjectDictModel extends BaseTenantModel {
    @Column(name = "object_id")
    private Long objectId;

    @Column(name = "dict_id")
    private Long dictId;
}
