package com.center.infrastructure.system.biz.role.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface RoleRepository extends JpaRepository<RoleModel,Long>, QuerydslPredicateExecutor<RoleModel> {

  RoleModel findByNameOrCode(String name,String code);

  RoleModel findByName(String name);

  RoleModel findByCode(String code);
}
