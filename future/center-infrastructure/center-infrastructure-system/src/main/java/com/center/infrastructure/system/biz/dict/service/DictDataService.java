package com.center.infrastructure.system.biz.dict.service;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.infrastructure.system.biz.dict.persistence.DictDataModel;
import com.center.infrastructure.system.biz.dict.persistence.ObjectDictModel;
import com.center.infrastructure.system.biz.dict.pojo.*;

import java.util.List;

public interface DictDataService {


    /**
     * 根据字典数据ID获取详情
     * @param id
     * @return
     */
    DictDataResp getDictDataById(Long id);
    /**
     * 创建分类数据
     * @param dictCreateReq-请求参数
     */
    void createData(DictCreateDataReq dictCreateReq);

    /**
     * 查询分类数据
     * @param dictId -类型
     * @param dictName -行业名称（模糊查询）
     * @param statusEnum -状态（ACTIVE/INACTIVE）
     * @return -分类列表
     */
    List<DictDataResp> selectData(Long dictId, String dictName, CommonStatusEnum statusEnum);

    /**
     * 删除分类
     * @param id -分类id
     */
    void delete(Long id);

    /**
     * 修改分类数据
     * @param dictUpdateDataReq
     */
    void updateData(DictUpdateDataReq dictUpdateDataReq);

    /**
     * 根据objectId和字典分类ID查找对应的字典值。
     * 必须要加上字典分类ID，因为有可能一个object会对应多个字典值。
     * @param objectId
     * @param dictTypId
     * @return
     */
    ObjectDictModel getByObjectIdAndDictTypeId(Long objectId, Long dictTypId);
}
