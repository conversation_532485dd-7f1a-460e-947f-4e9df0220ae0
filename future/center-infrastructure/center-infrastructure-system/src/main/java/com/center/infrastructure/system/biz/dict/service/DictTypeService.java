package com.center.infrastructure.system.biz.dict.service;

import com.center.infrastructure.system.biz.dict.pojo.DictCreateTypeReq;
import com.center.infrastructure.system.biz.dict.pojo.DictTypeResp;
import com.center.infrastructure.system.biz.dict.pojo.DictUpdateTypeReq;

import java.util.List;

public interface DictTypeService {

    /**
     * 创建分类类型
     * @param dictCreateTypeReq-创建分类类型请求
     */
    void createType(DictCreateTypeReq dictCreateTypeReq);

    /**
     * 查询分类类型
     * @return -类型列表
     */
    List<DictTypeResp> selectType();

    /**
     * 更新分类类型
     * @param dictUpdateTypeReq-更新分类类型请求
     */
    void updateType(DictUpdateTypeReq dictUpdateTypeReq);
}
