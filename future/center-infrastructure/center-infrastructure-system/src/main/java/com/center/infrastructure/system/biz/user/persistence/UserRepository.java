package com.center.infrastructure.system.biz.user.persistence;

import com.center.framework.common.enumerate.CommonStatusEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRepository extends JpaRepository<UserModel, Long>,
    QuerydslPredicateExecutor<UserModel> {

  UserModel findByUsernameAndTenantId(String userName, Long TenantId);

  Long countByTenantIdAndStatus(Long tenantId, CommonStatusEnum status);

  Long countByTenantId(Long tenantId);

  UserModel findByUsernameAndPassword(String username,String password);

  boolean existsByDepartId(Long id);

}
