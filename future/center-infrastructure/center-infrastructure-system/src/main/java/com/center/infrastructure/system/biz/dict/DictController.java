package com.center.infrastructure.system.biz.dict;

import cn.hutool.core.util.StrUtil;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.dict.enumeration.DictTypeEnum;
import com.center.infrastructure.system.biz.dict.pojo.*;
import com.center.infrastructure.system.biz.dict.service.DictDataService;
import com.center.infrastructure.system.biz.dict.service.DictTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 字典管理
 * @date 2024/12/26 13:12
 */
@Tag(name = "系统基础功能-字典管理")
@RestController
@RequestMapping("/system/dict")
@Validated
@Slf4j
public class DictController {
    @Resource
    private DictDataService dictDataService;
    @Resource
    private DictTypeService dictTypeService;


    @Operation(summary = "根据字典数据ID获取详情")
    @GetMapping("/get_by_id/{id}")
    public CommonResult<DictDataResp> getDictDataById(@PathVariable(value = "id") Long id){
        return CommonResult.success(dictDataService.getDictDataById(id));
    }
    @Operation(summary = "创建字典数据")
    @PostMapping("/create_data")
    public CommonResult<String> createData(@RequestBody @Valid DictCreateDataReq dictCreateReq) {
        dictDataService.createData(dictCreateReq);
        return CommonResult.successWithMessageOnly("创建字典数据成功");
    }

    @Operation(summary = "创建字典类型")
    @PostMapping("/create_type")
    public CommonResult<String> createType(@RequestBody @Valid DictCreateTypeReq dictCreateTypeReq) {
        dictTypeService.createType(dictCreateTypeReq);
        return CommonResult.successWithMessageOnly("创建分类成功");
    }

    @Operation(summary = "删除字典数据")
    @Parameter(description = "具体分类id")
    @PostMapping("/delete/{id}")
    public CommonResult<String> delete(@PathVariable(value = "id") Long id) {
        dictDataService.delete(id);
        return CommonResult.successWithMessageOnly("删除成功");
    }

    @Operation(summary = "修改字典数据")
    @PostMapping("/update_data")
    public CommonResult<String> updateData(@RequestBody @Valid DictUpdateDataReq dictUpdateDataReq) {
        dictDataService.updateData(dictUpdateDataReq);
        return CommonResult.successWithMessageOnly("修改成功");
    }

    @Operation(summary = "修改字典类型")
    @PostMapping("/update_type")
    public CommonResult<String> updateType(@RequestBody @Valid DictUpdateTypeReq dictUpdateTypeReq) {
        dictTypeService.updateType(dictUpdateTypeReq);
        return CommonResult.successWithMessageOnly("修改成功");
    }

    @Operation(summary = "查询所有字典类型")
    @GetMapping("/select_type")
    public CommonResult<List<DictTypeResp>> selectType() {
        return CommonResult.success(dictTypeService.selectType());
    }

    @Parameter(description = "字典类型")
    @Operation(summary = "根据字典类型查询字典数据列表")
    @GetMapping("/select_data")
    public CommonResult<List<DictDataResp>> selectData(@RequestParam(name = "dictType") DictTypeEnum dictType
            ,@RequestParam(name = "dictName")String dictName) {
        return CommonResult.success(dictDataService.selectData(dictType.getValue(),dictName,null));
    }

    @Parameter(description = "字典类型")
    @Operation(summary = "根据字典类型查询字典数据列表-不需要登录")
    @GetMapping("/public/select_data")
    public CommonResult<List<DictDataResp>> publicSelectData(@RequestParam(name = "dictType") DictTypeEnum dictType) {
        return CommonResult.success(dictDataService.selectData(dictType.getValue(), StrUtil.EMPTY, CommonStatusEnum.ACTIVE));
    }

}
