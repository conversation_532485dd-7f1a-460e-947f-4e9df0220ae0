package com.center.infrastructure.system.biz.dict.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DictTypeRepository extends JpaRepository<DictTypeModel, Long> {

    boolean existsByDictNameOrDictType(String dictName, String dictType);

    boolean existsByDictName(String dictName);

    boolean existsByDictType(String dictType);
}
