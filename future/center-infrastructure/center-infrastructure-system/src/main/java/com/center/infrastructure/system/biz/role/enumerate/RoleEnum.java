package com.center.infrastructure.system.biz.role.enumerate;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @description: 角色枚举类
 * @date 2024/10/23 13:21
 */
@AllArgsConstructor
public enum RoleEnum implements IEnumerate<String> {
    SUPERADMIN("SUPERADMIN","超级管理员"),
    ADMIN("ADMIN","管理员"),
    EMPLOYEE("EMPLOYEE","员工"),
    CUSTOM("CUSTOM","自定义用户");
    ;
    private String value;
    private String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
