package com.center.infrastructure.system.biz.tenant;

import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.tenant.pojo.TenantCreateReq;
import com.center.infrastructure.system.biz.tenant.pojo.TenantResp;
import com.center.infrastructure.system.biz.tenant.pojo.TenantUpdateReq;
import com.center.infrastructure.system.biz.tenant.service.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "系统基础功能-租户管理")
@RestController
@RequestMapping("/system/tenant")
@Validated
public class TenantController {

  @Resource
  private TenantService tenantService;

  @GetMapping("/get")
  @Operation(summary = "根据租户ID查询租户信息",description = "")
  @Parameter(name = "id",description = "租户ID",example = "1")
  public CommonResult<TenantResp> get(@RequestParam(name = "id") Long id){
    return CommonResult.success(tenantService.get(id));
  }

  @PostMapping("/create")
  @Operation(summary = "创建租户",description = "创建新的租户（名称不能重复）")
  @Parameter(description = "新的租户基本信息")
  public CommonResult<String> save(@RequestBody @Valid TenantCreateReq tenantCreateReq){
    tenantService.save(tenantCreateReq);
    return CommonResult.success();
  }

  @PostMapping("/update")
  @Operation(summary = "修改租户基本信息",description = "新的租户名称不能重复")
  @Parameter(description = "租户新的基本信息")
  public CommonResult<String> update(@RequestBody @Valid TenantUpdateReq tenantUpdateReq){
    tenantService.update(tenantUpdateReq);
    return CommonResult.success();
  }

  @PostMapping("/delete")
  @Operation(summary = "根据租户ID删除租户",description = "")
  @Parameter(name = "id",description = "租户ID",example = "1")
  public CommonResult<String> delete(@RequestParam(name = "id") Long id){
    tenantService.delete(id);
    return CommonResult.success();
  }
}
