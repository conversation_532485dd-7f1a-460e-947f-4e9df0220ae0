package com.center.infrastructure.system.biz.menu.persistence;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface MenuRepository extends JpaRepository<MenuModel, Long>,
    QuerydslPredicateExecutor<MenuModel> {

  Long countByParentId(Long id);

}
