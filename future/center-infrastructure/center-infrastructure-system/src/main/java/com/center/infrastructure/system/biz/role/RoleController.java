package com.center.infrastructure.system.biz.role;

import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.role.pojo.RoleCreateReq;
import com.center.infrastructure.system.biz.role.pojo.RoleMenuReq;
import com.center.infrastructure.system.biz.role.pojo.RoleResp;
import com.center.infrastructure.system.biz.role.pojo.RoleUpdateReq;
import com.center.infrastructure.system.biz.role.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "系统基础功能-角色管理")
@RestController
@RequestMapping("/system/role")
@Validated
public class RoleController {

  @Resource
  private RoleService roleService;

  @GetMapping("/get")
  @Operation(summary = "根据角色ID获取角色详细信息",description = "")
  @Parameter(name = "id",description = "角色ID",example = "1")
  public CommonResult<RoleResp> get(@Valid @RequestParam(name = "id")Long id){
    return CommonResult.success(roleService.get(id));
  }

  @PostMapping("/create")
  @Operation(summary = "创建一个新的角色",description = "")
  @Parameter(description = "新的角色详细信息")
  public CommonResult<String> save(@RequestBody @Valid RoleCreateReq roleCreateReq){
    roleService.save(roleCreateReq);
    return CommonResult.success();
  }

  @PostMapping("/update")
  @Operation(summary = "更新角色信息",description = "")
  @Parameter(description = "角色详细信息")
  public CommonResult<String> update(@RequestBody @Valid RoleUpdateReq roleUpdateReq){
    roleService.update(roleUpdateReq);
    return CommonResult.success();
  }

  @PostMapping("/delete")
  @Operation(summary = "删除角色")
  @Parameter(description = "角色ID")
  public CommonResult<String> delete(@Valid @RequestParam(name = "id")Long id){
    roleService.delete(id);
    return CommonResult.success();
  }

  @PostMapping("/create_role_menu")
  @Operation(summary = "给角色分配菜单权限")
  @Parameter(description = "角色与所属菜单")
  public CommonResult<String> saveRoleMenu(@Valid @RequestBody RoleMenuReq roleMenuReq){
    roleService.saveRoleMenu(roleMenuReq);
    return CommonResult.success();
  }
}
