package com.center.infrastructure.system.biz.role.persistence;


import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseModel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.Data;

@Table(name = "center_system_role")
@Entity
@Data
public class RoleModel extends BaseModel {

  @Column(name = "name" ,nullable = false)
  private String name;

  @Column(name = "code" ,nullable = false)
  private String code;

  @Column(name = "sort" ,nullable = false)
  private Integer sort;

  @Column(name = "status" ,nullable = false)
  @Enumerated(EnumType.STRING)
  private CommonStatusEnum status;

  @Column(name = "remark")
  private String remark;

  @Column(name = "tenant_id",nullable = false)
  private Long tenantId;
}
