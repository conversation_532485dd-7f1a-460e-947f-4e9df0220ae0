package com.center.infrastructure.system.biz.dict.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;

@Data
public class DictCreateTypeReq {
    @Schema(description = "字典类型")
    @NotEmpty(message = "字典类型不能为空")
    private String dictType;

    @Schema(description = "字典名称")
    @NotEmpty(message = "字典名称不能为空")
    private String dictName;

    @Schema(description = "字典描述")
    private String remark;
}
