package com.center.infrastructure.system.biz.depart.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 部门响应类
 */
@Data
public class DepartResp {

    @Schema(description = "部门ID")
    private Long id;

    @Schema(description = "部门名称")
    private String departName;

    @Schema(description = "上级部门ID")
    private Long parentId;

    @Schema(description = "显示顺序")
    private Integer sort;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "创建者ID")
    private Long creatorId;

    @Schema(description = "更新者ID")
    private Long updaterId;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "更新时间")
    private String updateTime;
}
