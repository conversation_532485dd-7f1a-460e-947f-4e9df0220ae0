package com.center.infrastructure.system.biz.dict.persistence;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;

/**
 * <AUTHOR>
 * @description: 字典数据实体类
 * @date 2024/12/26 13:20
 */
@Table(name = "center_system_dict_data")
@Entity
@Data
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class DictDataModel extends BaseTenantModel {

    @Column(name = "dict_id")
    private Long dictId;

    @Column(name = "dict_label")
    private String dictLabel;

    @Column(name = "dict_value")
    private String dictValue;

    @Column(name = "is_default")
    private Integer isDefault;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private CommonStatusEnum status;

    @Column(name = "sort")
    private Integer sort;

    @Column(name = "remark")
    private String remark;
}
