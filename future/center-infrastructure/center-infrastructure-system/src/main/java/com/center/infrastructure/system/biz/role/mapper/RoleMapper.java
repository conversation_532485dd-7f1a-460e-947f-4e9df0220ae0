package com.center.infrastructure.system.biz.role.mapper;

import com.center.infrastructure.system.biz.role.persistence.RoleModel;
import com.center.infrastructure.system.biz.role.pojo.RoleCreateReq;
import com.center.infrastructure.system.biz.role.pojo.RoleResp;
import com.center.infrastructure.system.biz.role.pojo.RoleUpdateReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RoleMapper {

    RoleMapper INSTANCE = Mappers.getMapper(RoleMapper.class);

    RoleModel toRoleModel(RoleCreateReq roleCreateReq);

    RoleModel toRoleModel(RoleUpdateReq roleUpdateReq);

    RoleResp toRoleResp(RoleModel roleModel);
}
