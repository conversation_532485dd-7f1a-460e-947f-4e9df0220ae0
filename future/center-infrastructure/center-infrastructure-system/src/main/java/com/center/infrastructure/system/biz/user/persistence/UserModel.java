package com.center.infrastructure.system.biz.user.persistence;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseTenantModel;
import java.time.LocalDateTime;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.Data;

@Table(name = "center_system_user")
@Entity
@Data
public class UserModel extends BaseTenantModel {

  @Column(name = "username",nullable = false)
  private String username;

  @Column(name = "password",nullable = false)
  private String password;

  @Column(name = "display_name")
  private String displayName;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private CommonStatusEnum status;

  @Column(name = "login_ip")
  private String loginIp;

  @Column(name = "login_time")
  private LocalDateTime loginTime;

  @Column(name = "depart_id")
  private Long departId;
}
