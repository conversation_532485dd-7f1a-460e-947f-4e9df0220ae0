package com.center.infrastructure.system.biz.tenant.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 企业信息
 * @date 2024/10/23 15:57
 */
@Data
public class TenantUpdateResp {
    @Schema(description = "租户ID")
    private Long id;

    @Schema(description = "企业名称")
    private String name;

    @Schema(description = "企业管理员姓名")
    private String managerName;

    @Schema(description = "企业管理员手机号")
    private String phoneNumber;

    @Schema(description = "联系邮箱")
    private String email;
}
