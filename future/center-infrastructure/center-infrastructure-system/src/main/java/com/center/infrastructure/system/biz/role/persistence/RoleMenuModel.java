package com.center.infrastructure.system.biz.role.persistence;

import com.center.framework.db.core.BaseModel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

@Table(name = "center_system_role_menu")
@Entity
@Data
public class RoleMenuModel extends BaseModel {

  @Column(name = "role_id",nullable = false)
  private Long roleId;

  @Column(name = "menu_id",nullable = false)
  private Long menuId;
}
