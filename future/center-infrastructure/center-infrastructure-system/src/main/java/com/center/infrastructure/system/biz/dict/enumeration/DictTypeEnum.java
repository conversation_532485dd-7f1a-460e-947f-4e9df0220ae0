package com.center.infrastructure.system.biz.dict.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum DictTypeEnum implements IEnumerate<Long> {


//    字典分类每增加一种，需要添加对应的分类
    CLASSIFICATION(1873900868047208448L, "CLASSIFICATION");

    private Long value;

    private String description;


    @Override
    public Long getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    // 静态方法，根据value获取对应的枚举
    public static DictTypeEnum fromValue(Long value) {
        for (DictTypeEnum type : DictTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null; // 如果没有找到匹配的value，返回null
    }

}