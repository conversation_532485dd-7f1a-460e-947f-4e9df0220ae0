package com.center.infrastructure.system.biz.tenant.persitence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface TenantRepository extends JpaRepository<TenantModel,Long>,
    QuerydslPredicateExecutor<TenantModel> {

  TenantModel findByName(String name);

}
