package com.center.infrastructure.system.biz.user.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRoleRepository extends JpaRepository<UserRoleModel,Long>,
    QuerydslPredicateExecutor<UserRoleModel> {

  Long countByRoleId(Long roleId);
}
