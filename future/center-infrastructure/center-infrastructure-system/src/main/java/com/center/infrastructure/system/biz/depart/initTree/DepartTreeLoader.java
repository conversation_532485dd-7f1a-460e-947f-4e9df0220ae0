package com.center.infrastructure.system.biz.depart.initTree;

import com.center.infrastructure.system.biz.depart.service.DepartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 部门树启动加载类
 * @date 2024/11/5 15:27
 */
@Component
@Slf4j
public class DepartTreeLoader implements CommandLineRunner {
    @Autowired
    private DepartService departService;

    @Override
    public void run(String... args) throws Exception {
        /*log.info("开始加载租户部门树");
        departService.loadAllTenantDepartTrees();
        log.info("租户部门树加载完成");
        log.info("开始加载部门树");
        departService.loadAllDepartTrees();
        log.info("部门树加载完成");*/
    }
}
