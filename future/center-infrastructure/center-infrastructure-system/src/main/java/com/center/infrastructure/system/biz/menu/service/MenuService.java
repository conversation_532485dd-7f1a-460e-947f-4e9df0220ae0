package com.center.infrastructure.system.biz.menu.service;

import cn.hutool.core.lang.tree.Tree;
import com.center.infrastructure.system.biz.menu.pojo.MenuCreateReq;
import com.center.infrastructure.system.biz.menu.pojo.MenuResp;
import com.center.infrastructure.system.biz.menu.pojo.MenuUpdateReq;
import java.util.List;


public interface MenuService {

  void save(MenuCreateReq menuCreateReq);

  void update(MenuUpdateReq menuUpdateReq);

  void delete(Long id);

  MenuResp get(Long id);

  List<Tree<String>> getAllMenu();
}
