package com.center.infrastructure.system.biz.user.persistence;

import com.center.framework.db.core.BaseModel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Table(name = "system_user_role")
@Entity
public class UserRoleModel extends BaseModel {

  @Column(name = "user_id",nullable = false)
  private Long userId;

  @Column(name = "role_id",nullable = false)
  private Long roleId;
}
