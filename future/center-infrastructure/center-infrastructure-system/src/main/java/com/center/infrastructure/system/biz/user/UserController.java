package com.center.infrastructure.system.biz.user;

import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.user.pojo.UserCreateReq;
import com.center.infrastructure.system.biz.user.pojo.UserResp;
import com.center.infrastructure.system.biz.user.pojo.UserRoleCreateReq;
import com.center.infrastructure.system.biz.user.pojo.UserRoleUpdateReq;
import com.center.infrastructure.system.biz.user.pojo.UserUpdateReq;
import com.center.infrastructure.system.biz.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "系统基础功能-用户管理")
@RestController
@RequestMapping("/system/user")
@Validated
@Slf4j
public class UserController {

  @Resource
  private UserService userService;

  @PostMapping("/create")
  @Operation(summary = "创建租户下的用户",description = "")
  @Parameter(description = "新用户的基本信息")
  public CommonResult<String> save(@RequestBody @Valid UserCreateReq userCreateReq){
    userService.save(userCreateReq);
    return CommonResult.success();
  }

  @PostMapping("/update")
  @Parameter(description = "新的用户信息")
  @Operation(summary = "修改用户基本",description = "")
  public CommonResult<String> update(@RequestBody @Valid UserUpdateReq userUpdateReq){
    userService.update(userUpdateReq);
    return CommonResult.success();
  }

  @PostMapping("/delete")
  @Parameter(description = "用户ID")
  @Operation(summary = "删除用户",description = "")
  public CommonResult<String> delete(@RequestParam(name = "id")Long id){
    userService.delete(id);
    return CommonResult.success();
  }

  @GetMapping("/get")
  @Parameter(description = "用户ID")
  @Operation(summary = "根据ID查看用户基本信息",description = "")
  public CommonResult<UserResp> get(@RequestParam(name = "id")Long id){
    return CommonResult.success(userService.get(id));
  }

  @PostMapping("/create_user_role")
  @Operation(summary = "给用户分配角色")
  @Parameter(description = "用户所属角色")
  public CommonResult<String> createUserRole(@Valid @RequestBody UserRoleCreateReq userRoleCreateReq){
    userService.saveUserRole(userRoleCreateReq);
    return CommonResult.success();
  }

  @PostMapping("/update_user_role")
  @Operation(summary = "修改用户所属角色")
  @Parameter(description = "用户所属角色")
  public CommonResult<String> updateUserRole(@Valid @RequestBody UserRoleUpdateReq userRoleUpdateReq){
    userService.updateUserRole(userRoleUpdateReq);
    return CommonResult.success();
  }
}
