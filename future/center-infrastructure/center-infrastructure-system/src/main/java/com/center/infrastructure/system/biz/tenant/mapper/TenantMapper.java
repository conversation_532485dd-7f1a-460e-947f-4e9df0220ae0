package com.center.infrastructure.system.biz.tenant.mapper;

import com.center.infrastructure.system.biz.tenant.persitence.TenantModel;
import com.center.infrastructure.system.biz.tenant.pojo.TenantCreateReq;
import com.center.infrastructure.system.biz.tenant.pojo.TenantResp;
import com.center.infrastructure.system.biz.tenant.pojo.TenantUpdateReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TenantMapper {

    TenantMapper INSTANCE = Mappers.getMapper(TenantMapper.class);

    TenantModel toTenantModel(TenantCreateReq tenantCreateReq);

    TenantModel toTenantModel(TenantUpdateReq tenantUpdateReq);

    TenantResp toTenantResp(TenantModel tenantModel);
}
