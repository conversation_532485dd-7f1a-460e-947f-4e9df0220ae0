package com.center.infrastructure.system.biz.dict.persistence;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;

/**
 * <AUTHOR>
 * @description: 字典类型实体类
 * @date 2024/12/26 13:13
 */
@Table(name = "center_system_dict_type")
@Entity
@Data
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class DictTypeModel extends BaseTenantModel {

    @Column(name = "dict_type")
    private String dictType;

    @Column(name = "dict_name")
    private String dictName;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private CommonStatusEnum status;

    @Column(name = "remark")
    private String remark;

}
