package com.center.infrastructure.system.biz.menu.enumerate;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum MenuCategoryEnum implements IEnumerate<String> {
  MENU("MENU","菜单"),
  BUTTON("BUTTON","按钮");

  private String value;
  private String description;

  @Override
  public String getValue() {
    return value;
  }

  @Override
  public String getDescription() {
    return description;
  }
}
