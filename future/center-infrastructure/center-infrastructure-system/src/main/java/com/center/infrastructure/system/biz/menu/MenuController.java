package com.center.infrastructure.system.biz.menu;

import cn.hutool.core.lang.tree.Tree;
import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.menu.pojo.MenuCreateReq;
import com.center.infrastructure.system.biz.menu.pojo.MenuResp;
import com.center.infrastructure.system.biz.menu.pojo.MenuUpdateReq;
import com.center.infrastructure.system.biz.menu.service.MenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "系统基础功能-菜单管理")
@RequestMapping("/system/menu")
@Validated
@RestController
public class MenuController {

  @Resource
  private MenuService menuService;


  @GetMapping("/get")
  @Operation(summary = "根据菜单ID获取菜单信息")
  @Parameter(name = "id",description = "菜单ID",example = "1")
  public CommonResult<MenuResp> get(@RequestParam(name = "id") Long id){
    return CommonResult.success(menuService.get(id));
  }

  @PostMapping("/create")
  @Operation(summary = "创建菜单")
  @Parameter(description = "菜单详细信息")
  public CommonResult<String> create(@RequestBody @Valid MenuCreateReq menuCreateReq){
    menuService.save(menuCreateReq);
    return CommonResult.success();
  }

  @PostMapping("/update")
  @Operation(summary = "修改菜单信息")
  @Parameter(description = "菜单详细信息")
  public CommonResult<String> update(@RequestBody @Valid MenuUpdateReq menuUpdateReq){
    menuService.update(menuUpdateReq);
    return CommonResult.success();
  }

  @PostMapping("/delete")
  @Operation(summary = "根据菜单ID删除菜单")
  @Parameter(name = "id",description = "菜单ID",example = "1")
  public CommonResult<String> delete(@RequestParam(name = "id") Long id){
    menuService.delete(id);
    return CommonResult.success();
  }

  @GetMapping("/get_all_menu")
  public CommonResult<List<Tree<String>>> getAllMenu(){
    return CommonResult.success(menuService.getAllMenu());
  }
}
