package com.center.infrastructure.system.biz.tenant.service;


import com.center.infrastructure.system.biz.tenant.pojo.TenantCreateReq;
import com.center.infrastructure.system.biz.tenant.pojo.TenantResp;
import com.center.infrastructure.system.biz.tenant.pojo.TenantUpdateReq;

public interface TenantService {

  void save(TenantCreateReq tenantCreateReq);

  void update(TenantUpdateReq tenantUpdateReq);

  TenantResp get(Long id);

  void delete(Long id);
}
