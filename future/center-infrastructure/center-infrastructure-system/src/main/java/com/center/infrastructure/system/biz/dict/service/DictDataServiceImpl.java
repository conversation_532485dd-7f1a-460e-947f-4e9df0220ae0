package com.center.infrastructure.system.biz.dict.service;

import cn.hutool.core.util.StrUtil;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.infrastructure.system.biz.dict.mapper.DictMapper;
import com.center.infrastructure.system.biz.dict.enumeration.DictTypeEnum;
import com.center.infrastructure.system.biz.dict.persistence.*;
import com.center.infrastructure.system.biz.dict.pojo.*;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description: 字典服务实现类
 * @date 2024/12/26 13:26
 */

/**
 * <AUTHOR>
 * @date 2024/12/31 10:28
 * @description 行业，数据分类管理实现类
 */
@Slf4j
@Service
public class DictDataServiceImpl implements DictDataService {
    @Resource
    private DictTypeRepository dictTypeRepository;
    @Resource
    private DictDataRepository dictDataRepository;

    @Resource
    private ObjectDictRepository objectDictRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Override
    public DictDataResp getDictDataById(Long id) {
        Optional<DictDataModel> optional = dictDataRepository.findById(id);
        if(!optional.isPresent()){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED
                    , "字典数据ID不存在！");
        }
        DictDataModel model= optional.get();
        DictDataResp dictDataResp = DictMapper.INSTANCE.toDictDataResp(optional.get());
        dictDataResp.setDictType(DictTypeEnum.fromValue(model.getDictId()));
        return dictDataResp;
    }

    @Override
    public void createData(DictCreateDataReq dictCreateReq) {
        checkDictLabel(dictCreateReq.getDictLabel(), dictCreateReq.getDictType().getValue());
        DictDataModel dictDataModel = DictMapper.INSTANCE.toDictDataModel(dictCreateReq);
        dictDataModel.setSort(1);
        dictDataModel.setStatus(CommonStatusEnum.ACTIVE);
        dictDataModel.setIsDefault(0);
        dictDataModel.setParentId(0L);
        dictDataModel.setRemark(dictCreateReq.getDictLabel());
        dictDataModel.setDictId(dictCreateReq.getDictType().getValue());
        dictDataRepository.save(dictDataModel);
    }

    @Override
    public List<DictDataResp> selectData(Long dictId,String dictName,CommonStatusEnum statusEnum) {
        QDictDataModel qDictDataModel = QDictDataModel.dictDataModel;
        QDictTypeModel qDictTypeModel = QDictTypeModel.dictTypeModel;
        BooleanBuilder builder= new BooleanBuilder();
        builder.and(qDictDataModel.dictId.eq(dictId));
        builder.and(qDictDataModel.dictId.eq(qDictTypeModel.id));
        if(StrUtil.isNotBlank(dictName)){
            builder.and(qDictDataModel.dictLabel.contains(dictName));
        }
        if(null != statusEnum){
            builder.and(qDictDataModel.status.eq(statusEnum));
        }

        List<Tuple> tupleList = jpaQueryFactory.select(qDictDataModel.id
                ,qDictDataModel.dictLabel,qDictDataModel.dictValue,qDictDataModel.isDefault
                ,qDictTypeModel.dictType,qDictTypeModel.dictName)
                .from(qDictDataModel,qDictTypeModel)
                .where(builder)
                .orderBy(qDictDataModel.sort.asc())
                .fetch();
        List<DictDataResp> dictDataRespList = new ArrayList<>();
        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()){
            Tuple tuple = iterator.next();
            DictDataResp dictDataResp = new DictDataResp();
            dictDataResp.setId(tuple.get(qDictDataModel.id));
            dictDataResp.setDictLabel(tuple.get(qDictDataModel.dictLabel));
            dictDataResp.setDictValue(tuple.get(qDictDataModel.dictValue));
            dictDataResp.setIsDefault(tuple.get(qDictDataModel.isDefault));
            dictDataResp.setDictType(DictTypeEnum.valueOf(tuple.get(qDictTypeModel.dictType)));
            dictDataResp.setDictTypeName(tuple.get(qDictTypeModel.dictName));
            dictDataRespList.add(dictDataResp);
        }
        return dictDataRespList;
    }

    @Override
    @Transactional
    public void delete(Long id) {
        DictDataModel dictDataModel = dictDataRepository.findById(id).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "字典项不存在")
        );
        if(dictDataModel.getIsDefault()==1){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "默认字典项不能删除");
        }
        if(objectDictRepository.countByDictId(id) >0 ){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "请先删除些行业分类下的数据！");
        }
        dictDataRepository.deleteById(id);
    }

    @Override
    public void updateData(DictUpdateDataReq dictUpdateDataReq) {
        DictDataModel dictDataModel = dictDataRepository.findById(dictUpdateDataReq.getId()).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "字典数据项不存在"));
        if(dictDataModel.getIsDefault()==1){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "默认字典项不能更新");
        }
        if(!dictDataModel.getDictLabel().equals(dictUpdateDataReq.getDictLabel())){
            checkDictLabel(dictUpdateDataReq.getDictLabel(), dictDataModel.getDictId());
        }
        dictDataRepository.save(DictMapper.INSTANCE.toDictDataModel(dictUpdateDataReq));
    }

    @Override
    public ObjectDictModel getByObjectIdAndDictTypeId(Long objectId, Long dictTypId) {
        QDictDataModel qDictDataModel = QDictDataModel.dictDataModel;
        QDictTypeModel qDictTypeModel = QDictTypeModel.dictTypeModel;
        QObjectDictModel qObjectDictModel = QObjectDictModel.objectDictModel;
        BooleanBuilder builder= new BooleanBuilder();
        builder.and(qObjectDictModel.objectId.eq(objectId));
        builder.and(qObjectDictModel.dictId.eq(qDictDataModel.id));
        builder.and(qDictDataModel.dictId.eq(qDictTypeModel.id));
        builder.and(qDictTypeModel.id.eq(dictTypId));
        ObjectDictModel objectDictModel = jpaQueryFactory.select(qObjectDictModel)
                .where(builder)
                .fetchOne();
        return objectDictModel;
    }

    //检测字典数据是否重名
    public void checkDictLabel(String label,Long dictId) {
        if (dictDataRepository.existsByDictLabelAndDictId(label,dictId)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "字典标签名已存在");
        }
    }

}
