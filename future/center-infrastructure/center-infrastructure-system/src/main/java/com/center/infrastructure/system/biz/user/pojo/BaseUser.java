package com.center.infrastructure.system.biz.user.pojo;

import cn.hutool.crypto.SecureUtil;
import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public abstract class BaseUser {

  @Schema(description = "用户名",requiredMode = RequiredMode.REQUIRED,example = "admin")
  @Length(min = 3, max = 20, message = "用户名长度为 3-20 位")
  @Pattern(regexp = "^[a-zA-Z0-9]{3,20}$", message = "用户账号由数字和字母组成")
  private String username;

  @Schema(description = "密码不能为空",requiredMode = RequiredMode.REQUIRED,example = "1@3aps")
  @Length(min = 6, max = 10, message = "密码长度为6-10位")
  private String password;

  @Schema(description = "显示名",example = "管理员")
  @Length(min = 2,max = 20,message = "显示名称长度为2到10个字符")
  private String displayName;

  @Schema(description = "用户状态",requiredMode = RequiredMode.REQUIRED,example = "ACTIVE")
  @NotNull(message = "用户状态不能为空")
  @EnumValidate(message = "用户状态不正确",value = CommonStatusEnum.class)
  private String status;

  @Schema(description = "租户ID",requiredMode = RequiredMode.REQUIRED,example = "1")
  @NotNull(message = "租户ID不能为空")
  private Long tenantId;

  public String getMD5Password(){
    return SecureUtil.md5(getPassword());
  }
}
