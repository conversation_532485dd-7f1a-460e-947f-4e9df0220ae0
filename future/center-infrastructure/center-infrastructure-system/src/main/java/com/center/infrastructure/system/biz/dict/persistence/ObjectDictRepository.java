package com.center.infrastructure.system.biz.dict.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ObjectDictRepository extends JpaRepository<ObjectDictModel, Long> {

    List<ObjectDictModel> findByDictId(Long id);

    List<ObjectDictModel> findByObjectId(Long id);

    long countByDictId(Long dictId);
    void deleteAllByObjectId(Long id);
}
