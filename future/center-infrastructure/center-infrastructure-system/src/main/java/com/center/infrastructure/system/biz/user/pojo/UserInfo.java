package com.center.infrastructure.system.biz.user.pojo;

import com.center.framework.common.enumerate.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserInfo {
    @Schema(description = "用户id")
    private Long id;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "昵称")
    private String displayName;


    @Schema(description = "用户状态")
    private CommonStatusEnum status;

    @Schema(description = "租户ID")
    private Long tenantId;

}
