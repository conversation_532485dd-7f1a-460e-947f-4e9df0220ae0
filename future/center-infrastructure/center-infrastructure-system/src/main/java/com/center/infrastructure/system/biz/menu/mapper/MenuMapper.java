package com.center.infrastructure.system.biz.menu.mapper;

import com.center.infrastructure.system.biz.menu.persistence.MenuModel;
import com.center.infrastructure.system.biz.menu.pojo.MenuCreateReq;
import com.center.infrastructure.system.biz.menu.pojo.MenuResp;
import com.center.infrastructure.system.biz.menu.pojo.MenuUpdateReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MenuMapper {

    MenuMapper INSTANCE = Mappers.getMapper(MenuMapper.class);

    MenuResp toMenuResp(MenuModel menuModel);

    MenuModel toMenuModel(MenuCreateReq menuCreateReq);

    MenuModel toMenuModel(MenuUpdateReq menuUpdateReq);
}
