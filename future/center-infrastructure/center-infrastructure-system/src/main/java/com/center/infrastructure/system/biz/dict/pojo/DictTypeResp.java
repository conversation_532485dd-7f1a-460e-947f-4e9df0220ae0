package com.center.infrastructure.system.biz.dict.pojo;

import com.center.framework.common.enumerate.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DictTypeResp {
    @Schema(description = "字典类型id")
    private Long id;

    @Schema(description = "字典类型")
    private String dictType;

    @Schema(description = "字典类型名称")
    private String dictName;

    @Schema(description = "字典类型状态")
    private CommonStatusEnum status;

    @Schema(description = "备注")
    private String remark;
}
