package com.center.infrastructure.system.biz.depart.persistence;

import com.center.framework.db.core.BaseTenantModel;
import lombok.Data;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

/**
 * 部门实体类
 */
@Data
@Entity
@Table(name = "center_system_depart")
public class DepartModel extends BaseTenantModel {

    /**
     * 部门名称
     */
    @NotNull
    @Column(name = "name", nullable = false)
    private String departName;

    /**
     * 上级部门ID
     */
    @NotNull
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 显示顺序
     */
    @Column(name = "sort")
    private Integer sort;

}
