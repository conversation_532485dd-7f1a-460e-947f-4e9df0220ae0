package com.center.infrastructure.system.biz.role.pojo;

import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public abstract class BaseRole {

  @Schema(description = "角色名称",requiredMode = RequiredMode.REQUIRED,example = "管理员")
  @Length(min = 3, max = 20, message = "角色名长度为 3-20 位")
  private String name;

  @Schema(description = "角色代码",requiredMode = RequiredMode.REQUIRED,example = "ADMIN")
  @Length(min = 3, max = 20, message = "角色代码长度为 3-20 位")
  @Pattern(regexp = "^[a-zA-Z0-9]{3,20}$", message = "用户账号由数字和字母组成")
  private String code;

  @Schema(description = "角色显示排序",requiredMode = RequiredMode.REQUIRED,example = "1")
  @NotNull(message = "角色显示排序不能为空")
  private Integer sort;

  @Schema(description = "角色状态",requiredMode = RequiredMode.REQUIRED,example = "ACTIVE")
  @NotNull(message = "角色状态不能为空")
  @EnumValidate(message = "角色状态不正确",value = CommonStatusEnum.class)
  private String status;

  @Schema(description = "角色备注说明",example = "系统管理员权限")
  private String remark;

  @Schema(description = "角色所属租户ID",example = "1")
  private Long tenantId;
}
