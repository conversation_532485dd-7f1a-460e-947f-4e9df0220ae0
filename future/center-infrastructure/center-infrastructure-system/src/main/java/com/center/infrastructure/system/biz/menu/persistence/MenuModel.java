package com.center.infrastructure.system.biz.menu.persistence;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseModel;
import com.center.infrastructure.system.biz.menu.enumerate.MenuCategoryEnum;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.Data;

@Table(name = "center_system_menu")
@Entity
@Data
public class MenuModel extends BaseModel {

  @Column(name = "name",nullable = false)
  private String name;

  @Column(name = "category",nullable = false)
  @Enumerated(value = EnumType.STRING)
  private MenuCategoryEnum category;

  @Column(name = "sort",nullable = false)
  private Integer sort;

  @Column(name = "parent_id",nullable = false)
  private Long parentId;

  @Column(name = "path",nullable = false)
  private String path;

  @Column(name = "icon",nullable = false)
  private String icon;

  @Column(name = "status" ,nullable = false)
  @Enumerated(EnumType.STRING)
  private CommonStatusEnum status;
}
