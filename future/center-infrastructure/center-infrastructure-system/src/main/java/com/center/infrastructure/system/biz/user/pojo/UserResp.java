package com.center.infrastructure.system.biz.user.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class UserResp extends BaseUser{

  @Schema(description = "用户ID",example = "1")
  private Long id;

  @Schema(description = "用户最后新一次登录地点IP",example = "1")
  private String loginIp;

  @Schema(description = "用户最新一次登录时间",example = "2024-08-19 15:30:12")
  private LocalDateTime loginTime;
}
