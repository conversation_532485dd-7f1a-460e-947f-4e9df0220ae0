package com.center.infrastructure.system.biz.tenant.pojo;

import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.utils.datetime.DateTimeUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public abstract class BaseTenant {

  @Schema(description = "租户名称",requiredMode = RequiredMode.REQUIRED,example = "雪浪云")
  @NotBlank(message = "租户名称不能为空")
  @Size(max = 100,message = "租户名称长度为2到50")
  private String name;

  @Schema(description = "备注说明")
  @Size(max = 200,message = "租户备注说明长度为2到100")
  private String description;

  @Schema(description = "租户到期时间",example = "日期（时间戳）")
  @NotNull(message = "租户过期时间不能为空")
  @JsonFormat(pattern = DateTimeUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = DateTimeUtils.TIME_ZONE_DEFAULT)
  private LocalDateTime expireTime;

  @Schema(description = "租户下可创建账号数量",example = "10")
  @NotNull(message = "租户下可创建账号数量不能为空")
  private Integer accountCount;

  @Schema(description = "租户状态",example = "ACTIVE")
  @EnumValidate(message = "租户状态不正确",value = CommonStatusEnum.class)
  @NotNull(message = "租户状态不能为空")
  private String status;
}
