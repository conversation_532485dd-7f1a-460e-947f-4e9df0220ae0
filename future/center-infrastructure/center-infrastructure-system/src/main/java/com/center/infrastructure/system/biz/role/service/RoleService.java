package com.center.infrastructure.system.biz.role.service;

import com.center.infrastructure.system.biz.role.pojo.RoleCreateReq;
import com.center.infrastructure.system.biz.role.pojo.RoleMenuReq;
import com.center.infrastructure.system.biz.role.pojo.RoleResp;
import com.center.infrastructure.system.biz.role.pojo.RoleUpdateReq;

public interface RoleService {

  void save(RoleCreateReq roleCreateReq);

  void update(RoleUpdateReq roleUpdateReq);

  RoleResp get(Long id);

  void delete(Long id);

  void saveRoleMenu(RoleMenuReq roleMenuReq);
}
