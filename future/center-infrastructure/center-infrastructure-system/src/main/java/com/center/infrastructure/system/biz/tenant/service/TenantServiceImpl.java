package com.center.infrastructure.system.biz.tenant.service;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.infrastructure.system.biz.tenant.mapper.TenantMapper;
import com.center.infrastructure.system.biz.role.pojo.RoleCreateReq;
import com.center.infrastructure.system.biz.role.service.RoleService;
import com.center.infrastructure.system.biz.tenant.persitence.TenantModel;
import com.center.infrastructure.system.biz.tenant.persitence.TenantRepository;
import com.center.infrastructure.system.biz.tenant.pojo.TenantCreateReq;
import com.center.infrastructure.system.biz.tenant.pojo.TenantResp;
import com.center.infrastructure.system.biz.tenant.pojo.TenantUpdateReq;
import com.center.infrastructure.system.biz.user.persistence.UserRepository;
import java.util.Optional;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class TenantServiceImpl implements TenantService {

  @Resource
  private TenantRepository tenantRepository;

  @Resource
  private UserRepository userRepository;

  @Resource
  private RoleService roleService;

  @Override
  @Transactional(rollbackFor = Exception.class,timeout = 2)
  public void save(TenantCreateReq tenantCreateReq) {
    checkName(tenantCreateReq.getName());
    TenantModel tenantModel = TenantMapper.INSTANCE.toTenantModel(tenantCreateReq);
    tenantRepository.save(tenantModel);
  }

  @Override
  public void update(TenantUpdateReq tenantUpdateReq) {
    checkName(tenantUpdateReq.getId(),tenantUpdateReq.getName());
    TenantModel tenantModel = TenantMapper.INSTANCE.toTenantModel(tenantUpdateReq);
    tenantRepository.save(tenantModel);
  }

  @Override
  public TenantResp get(Long id) {
    return TenantMapper.INSTANCE.toTenantResp(getTenantModel(id));
  }

  @Override
  public void delete(Long id) {
    try {
      if(userRepository.countByTenantId(id) > 0){
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "请先删除此租户下的所有用户");
      }
      tenantRepository.deleteById(id);
    }catch (EmptyResultDataAccessException e){
      log.error("删除节点出错！",e);
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "租户不存在");
    }
  }

  private TenantModel getTenantModel(Long id){
    Optional<TenantModel> optional = tenantRepository.findById(id);
    if(optional.isPresent()){
      return optional.get();
    }else {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT,"租户不存在");
    }
  }

  private void checkName(Long id,String name){
    TenantModel tenantModel = getTenantModel(id);
    if(!tenantModel.getName().equals(name)){
      tenantModel = tenantRepository.findByName(name);
      if(null != tenantModel){
        throw ServiceExceptionUtil
            .exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的租户名称");
      }
    }
  }
  private void checkName(String name){
    TenantModel tenantModel = tenantRepository.findByName(name);
    if(null != tenantModel){
      throw ServiceExceptionUtil
          .exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的租户名称");
    }
  }

  /**
   * 创建租户时，默认给租户创建一个角色
   * @param tenantId
   */
  private void saveRole(Long tenantId){
    RoleCreateReq roleCreateReq = new RoleCreateReq();
    roleCreateReq.setName("管理员");
    roleCreateReq.setCode("ADMIN");
    roleCreateReq.setRemark("创建租户时默认创建一个角色");
    roleCreateReq.setSort(1);
    roleCreateReq.setStatus(CommonStatusEnum.ACTIVE.getValue());
    roleCreateReq.setTenantId(tenantId);
    roleService.save(roleCreateReq);
  }
}
