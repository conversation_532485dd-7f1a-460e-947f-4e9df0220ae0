package com.center.infrastructure.system.biz.depart.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 部门创建请求
 */
@Data
public class DepartCreateReq  {
    @NotNull(message = "部门名称不能为空")
    @Schema(description = "部门名称")
    private String departName;

    @NotNull(message = "上级部门ID不能为空")
    @Schema(description = "上级部门ID")
    private Long parentId;
}