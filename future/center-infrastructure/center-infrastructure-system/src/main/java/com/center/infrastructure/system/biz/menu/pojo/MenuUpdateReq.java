package com.center.infrastructure.system.biz.menu.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MenuUpdateReq extends BaseMenu {

  @Schema(description = "菜单ID",requiredMode = RequiredMode.REQUIRED,example = "1")
  @NotNull(message = "菜单ID不能为空")
  private Long id;
}
