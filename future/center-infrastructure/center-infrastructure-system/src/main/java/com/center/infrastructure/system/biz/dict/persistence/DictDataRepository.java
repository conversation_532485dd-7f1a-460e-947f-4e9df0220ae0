package com.center.infrastructure.system.biz.dict.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DictDataRepository extends JpaRepository<DictDataModel, Long> {
    Boolean existsByDictLabelAndDictId(String label,Long dictId);

    List<DictDataModel> findAllByDictIdOrderBySort(Long dictId);
}
