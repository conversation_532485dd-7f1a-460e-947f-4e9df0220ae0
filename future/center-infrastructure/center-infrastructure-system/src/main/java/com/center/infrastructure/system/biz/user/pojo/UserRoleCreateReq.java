package com.center.infrastructure.system.biz.user.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UserRoleCreateReq {

  @Schema(description = "用户ID",requiredMode = RequiredMode.REQUIRED,example = "1")
  @NotNull(message = "用户ID不能为空")
  private Long userId;

  @Schema(description = "角色ID",requiredMode = RequiredMode.REQUIRED,example = "1")
  @NotNull(message = "角色ID不能为空")
  private Long roleId;

}
