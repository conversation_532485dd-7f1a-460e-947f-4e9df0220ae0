package com.center.infrastructure.system.biz.tenant.persitence;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseModel;
import java.time.LocalDateTime;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "center_system_tenant")
public class TenantModel extends BaseModel {

  @Column(name = "name" ,nullable = false)
  private String name;

  @Column(name = "description")
  private String description;

  @Column(name = "expire_time",nullable = false)
  private LocalDateTime expireTime;

  @Column(name = "account_count",nullable = false)
  private int accountCount;

  @Column(name = "status",nullable = false)
  @Enumerated(EnumType.STRING)
  private CommonStatusEnum status;
}
