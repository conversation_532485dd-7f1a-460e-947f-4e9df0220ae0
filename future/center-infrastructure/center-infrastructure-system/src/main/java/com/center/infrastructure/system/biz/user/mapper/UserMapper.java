package com.center.infrastructure.system.biz.user.mapper;


import com.center.infrastructure.system.biz.user.persistence.UserModel;
import com.center.infrastructure.system.biz.user.persistence.UserRoleModel;
import com.center.infrastructure.system.biz.user.pojo.UserCreateReq;
import com.center.infrastructure.system.biz.user.pojo.UserResp;
import com.center.infrastructure.system.biz.user.pojo.UserRoleCreateReq;
import com.center.infrastructure.system.biz.user.pojo.UserRoleUpdateReq;
import com.center.infrastructure.system.biz.user.pojo.UserUpdateReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UserMapper {

    UserMapper INSTANCE = Mappers.getMapper(UserMapper.class);

    UserResp toUserResp(UserModel userModel);

    UserModel toUserModel(UserCreateReq userCreateReq);

    UserModel toUserModel(UserUpdateReq userUpdateReq);

    UserRoleModel toUserRoleModel(UserRoleCreateReq userRoleCreateReq);

    UserRoleModel toUserRoleModel(UserRoleUpdateReq userRoleUpdateReq);
}
