package com.center.infrastructure.system.biz.dict.pojo;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.infrastructure.system.biz.dict.enumeration.DictTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotNull;

@Data
public class DictUpdateDataReq extends BaseDictData{
    @Schema(description = "字典数据id")
    @NotNull(message = "字典数据id不能为空")
    private Long id;
}
