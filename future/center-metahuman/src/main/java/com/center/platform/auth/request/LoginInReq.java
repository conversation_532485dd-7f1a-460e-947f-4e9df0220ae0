package com.center.platform.auth.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class LoginInReq {
  @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "zhangsan")
  @NotBlank(message = "用户名不能为空")
  private String userName;

  @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
  @NotBlank(message = "密码不能为空")
  private String loginPassword;
}
