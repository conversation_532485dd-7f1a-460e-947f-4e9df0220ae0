package com.center.platform.deepseek;


import jakarta.annotation.Resource;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("chat")
public class DeepseekController {

    @Resource
    private ChatClient chatClient;

    @GetMapping(value = "/completion",produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> completion(@RequestParam(name = "message") String message){
        return chatClient.prompt()
                .user(message)
                .stream()
                .content();
    }
}
