package com.center.platform.example.config;

import lombok.AllArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@AllArgsConstructor
@Service
public class ChatService {

    private final ChatClient chatClient;

    public Flux<String> streamChat(String userMessage) {
        return chatClient
                .prompt()
                .system("你是一个乐于助人的 AI 助手。")
                .user(userMessage)
                .stream()
                .content(); // 返回 Flux<String>，逐 token/片段推送
    }
}
