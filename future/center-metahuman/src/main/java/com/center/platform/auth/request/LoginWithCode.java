package com.center.platform.auth.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class LoginWithCode {

    @Schema(description = "手机号",example = "13812341234")
    @NotBlank(message = "手机号不能为空！")
    private String phoneNumber;

    @Schema(description = "手机验证码",example = "112233")
    @NotBlank(message = "手机验证码不能为空！")
    private String smsCode;
}
