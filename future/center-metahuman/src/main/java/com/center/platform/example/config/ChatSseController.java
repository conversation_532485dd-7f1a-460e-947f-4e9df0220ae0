package com.center.platform.example.config;

import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicLong;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/chat")
public class ChatSseController {

    private final ChatService chatService;

    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<Object>> stream(@RequestParam("q") String q) {

        AtomicLong seq = new AtomicLong(0);

        // delta：模型的增量文本
        Flux<ServerSentEvent<Object>> deltas = chatService.streamChat(q)
                .map(token -> sse(seq, "delta", token));

        // ping：心跳，避免代理超时，也方便前端探活
        Flux<ServerSentEvent<Object>> pings = Flux.interval(Duration.ofSeconds(15))
                .takeUntilOther(deltas.then()) // 当 deltas 完成后，停止心跳
                .map(i -> sse(seq, "ping", ""));

        // meta：可选，发送一些上下文信息（演示用）
        Flux<ServerSentEvent<Object>> meta = Flux.just(sse(seq, "meta", "stream-start"));

        // done：流结束时补一个收尾事件
        Flux<ServerSentEvent<Object>> done = Flux.just(sse(seq, "done", ""));

        // 合并：meta -> deltas & pings（并行）-> done
        return Flux.concat(
                        meta,
                        Flux.merge(deltas, pings)
                ).concatWith(done)
                .onErrorResume(e ->
                        Flux.just(sse(seq, "app-error", e.getMessage()))
                );
    }

    private static ServerSentEvent<Object> sse(AtomicLong seq, String event, Object data) {
        return ServerSentEvent.builder(data)
                .id(String.valueOf(seq.incrementAndGet())) // 如需断点续传可利用此 id + Last-Event-ID
                .event(event)                               // 事件分类：delta/done/app-error/ping/meta...
                .retry(Duration.ofSeconds(3))                    // 浏览器自动重连间隔（ms，可选）
                .build();
    }
}