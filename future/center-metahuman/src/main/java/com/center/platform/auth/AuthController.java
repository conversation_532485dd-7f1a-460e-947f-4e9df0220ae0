package com.center.platform.auth;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.center.cache.factory.CacheFactory;
import com.center.cache.interfaces.Cache;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.jwt.JwtTokenProvider;
import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.center.infrastructure.system.biz.user.pojo.UserInfo;
import com.center.infrastructure.system.biz.user.service.UserService;
import com.center.platform.auth.request.LoginInReq;
import com.center.platform.auth.respeonse.LoginResp;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.INACTIVE_OBJECT;

@Tag(name = "管理后台 - 用户认证")
@RestController
@RequestMapping("/admin/auth")
@Validated
public class AuthController {

    @Resource
    JwtTokenProvider jwtTokenProvider;

    @Resource
    private UserService userService;

    @Resource
    private JPAQueryFactory jpaQueryFactory;
    @Resource
    private CacheFactory cacheFactory;

    @Value("${metahuman.jwt.expire-in}")
    private Long expireIn;

    @Operation(summary = "用户登录接口")
    @ResponseBody
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public CommonResult<LoginResp> load(@Valid @RequestBody LoginInReq loginInReq,
                                        HttpServletRequest request) {
        QUserModel qUserModel = QUserModel.userModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qUserModel.username.eq(loginInReq.getUserName()));
        builder.and(qUserModel.password.eq(SecureUtil.md5(loginInReq.getLoginPassword())));

        return CommonResult.success(getLoginResp(builder,request));
    }


    @Operation(summary = "获取用户登录信息")
    @ResponseBody
    @EnumConvertPoint
    @GetMapping(value = "/get_login_info")
    public CommonResult<UserInfo> getLoginInfo() {
        return CommonResult.success((UserInfo) cacheFactory.getHashCache().get(LoginContextHolder.getLoginUserId().toString()));
    }

    private LoginResp getLoginResp(BooleanBuilder builder, HttpServletRequest request){
        QUserModel qUserModel = QUserModel.userModel;
        UserInfo userInfo = jpaQueryFactory.select(Projections.bean(
                        UserInfo.class,
                        qUserModel.id,
                        qUserModel.username,
                        qUserModel.displayName,
                        qUserModel.departId,
                        qUserModel.tenantId,
                        qUserModel.status
                ))
                .from(qUserModel)
                .where(builder)
                .fetchFirst();
        if (userInfo == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_USERNAME_PASSWORD, "用户名密码错误,或手机号未注册！");
        } else {
            if (!userInfo.getStatus().equals(CommonStatusEnum.ACTIVE)) {
                throw ServiceExceptionUtil.exception(INACTIVE_OBJECT, "账户被停用，请联系管理员");
            }
        }
        LoginContextHolder.setLoginUserId(userInfo.getId());
        userService.updateUserLoginInfo(userInfo.getId(), JakartaServletUtil.getClientIP(request));
        String token = jwtTokenProvider.createToken(userInfo.getId(), userInfo.getTenantId());
        Cache cache = cacheFactory.getHashCache();
        cache.put(LoginContextHolder.getLoginUserId().toString(), userInfo, expireIn);
        return new LoginResp(token, userInfo.getId());
    }



}
